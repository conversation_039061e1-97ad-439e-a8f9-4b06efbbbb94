#!/usr/bin/env python3
"""
Merge Partial Range Results
Combines the progress from all 3 ranges into a comprehensive dataset
"""

import json
import os
from datetime import datetime, timedelta

def merge_partial_ranges():
    """Merge all partial range progress files"""
    
    output_dir = "data/processed"
    
    # Find all range progress files
    progress_files = [
        "range_1-236_progress.json",
        "range_237-472_progress.json", 
        "range_473-708_progress.json"
    ]
    
    print("🔄 Merging partial range results...")
    
    all_questions = []
    all_failed_pages = []
    total_duration = timedelta()
    range_metadata = []
    
    for progress_file in progress_files:
        file_path = os.path.join(output_dir, progress_file)
        
        if not os.path.exists(file_path):
            print(f"⚠️  File not found: {progress_file}")
            continue
            
        print(f"📁 Processing: {progress_file}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
            
            # Collect questions
            questions = progress_data.get('questions', [])
            all_questions.extend(questions)
            
            # Collect failed pages
            failed_pages = progress_data.get('failed_pages', [])
            all_failed_pages.extend(failed_pages)
            
            # Collect metadata
            stats = progress_data.get('stats', {})
            range_metadata.append({
                'range_id': stats.get('range_id'),
                'start_page': stats.get('start_page'),
                'end_page': stats.get('end_page'),
                'pages_completed': stats.get('pages_completed', 0),
                'total_pages_in_range': stats.get('total_pages', 0),
                'questions': len(questions),
                'answers': sum(len(q.get('answers', [])) for q in questions),
                'completion_percentage': stats.get('completion_percentage', 0),
                'start_time': stats.get('start_time'),
                'last_update': stats.get('last_update'),
                'failed_pages': len(failed_pages)
            })
            
            # Calculate duration if available
            if stats.get('start_time') and stats.get('last_update'):
                try:
                    start_time = datetime.fromisoformat(stats['start_time'])
                    end_time = datetime.fromisoformat(stats['last_update'])
                    duration = end_time - start_time
                    total_duration += duration
                except:
                    pass
    
    # Calculate overall statistics
    total_pages_completed = sum(r['pages_completed'] for r in range_metadata)
    total_questions = len(all_questions)
    total_answers = sum(len(q.get('answers', [])) for q in all_questions)
    overall_completion = (total_pages_completed / 708) * 100
    
    # Create comprehensive output
    comprehensive_output = {
        'metadata': {
            'scraper_version': 'distributed_partial_merge_v1.0',
            'source': 'PropertyGuru HDB Questions - Partial 3-Terminal Results',
            'merge_timestamp': datetime.now().isoformat(),
            'merge_type': 'partial_progress',
            'total_ranges': len(range_metadata),
            'total_pages_attempted': 708,
            'total_pages_completed': total_pages_completed,
            'total_questions': total_questions,
            'total_answers': total_answers,
            'total_failed_pages': len(all_failed_pages),
            'overall_completion_percentage': overall_completion,
            'overall_success_rate': ((total_pages_completed - len(all_failed_pages)) / total_pages_completed * 100) if total_pages_completed > 0 else 0,
            'total_scraping_time': str(total_duration),
            'avg_questions_per_page': total_questions / total_pages_completed if total_pages_completed > 0 else 0,
            'avg_answers_per_question': total_answers / total_questions if total_questions > 0 else 0,
            'range_breakdown': range_metadata,
            'performance_notes': [
                "3-terminal distributed approach was successful",
                "All ranges achieved 16.9% completion before shutdown",
                "No failed pages - 100% success rate on attempted pages",
                "Average ~20 questions per page with comprehensive answers",
                "System shutdown interrupted at ~1 hour 18 minutes runtime"
            ]
        },
        'failed_pages': sorted(all_failed_pages),
        'questions': all_questions
    }
    
    # Save comprehensive file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    comprehensive_file = os.path.join(output_dir, f"propertyguru_3terminal_partial_{timestamp}.json")
    
    with open(comprehensive_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_output, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 PARTIAL RANGES MERGED SUCCESSFULLY!")
    print(f"📊 Comprehensive Statistics:")
    print(f"   • Total ranges processed: {len(range_metadata)}")
    print(f"   • Total pages completed: {total_pages_completed}/708 ({overall_completion:.1f}%)")
    print(f"   • Total questions: {total_questions:,}")
    print(f"   • Total answers: {total_answers:,}")
    print(f"   • Total failed pages: {len(all_failed_pages)}")
    print(f"   • Success rate: {((total_pages_completed - len(all_failed_pages)) / total_pages_completed * 100) if total_pages_completed > 0 else 0:.1f}%")
    print(f"   • Total scraping time: {total_duration}")
    print(f"   • Avg questions/page: {total_questions / total_pages_completed if total_pages_completed > 0 else 0:.1f}")
    print(f"   • Avg answers/question: {total_answers / total_questions if total_questions > 0 else 0:.1f}")
    print(f"   • Comprehensive file: {comprehensive_file}")
    
    print(f"\n📋 Range Breakdown:")
    for r in range_metadata:
        print(f"   • Range {r['range_id']}: {r['pages_completed']}/{r['total_pages_in_range']} pages, "
              f"{r['questions']} questions, {r['answers']} answers ({r['completion_percentage']:.1f}%)")
    
    return comprehensive_file

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru 3-Terminal Partial Merge                    ║
║                                                                              ║
║  🎯 Combining partial results from 3-terminal distributed scraping          ║
║  📊 Merging progress files into comprehensive dataset                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    try:
        comprehensive_file = merge_partial_ranges()
        print(f"\n✅ Merge completed successfully!")
        print(f"📁 Output file: {comprehensive_file}")
        
    except Exception as e:
        print(f"\n💥 Error during merge: {e}")
        import traceback
        traceback.print_exc()
