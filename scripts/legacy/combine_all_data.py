#!/usr/bin/env python3
"""
Combine ALL PropertyGuru Data
Merges all available PropertyGuru data sources into one comprehensive dataset
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any

def load_json_file(file_path: str) -> Dict[str, Any]:
    """Load JSON file safely"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"⚠️  Error loading {file_path}: {e}")
        return {}

def extract_questions_from_data(data: Dict[str, Any], source_file: str) -> List[Dict[str, Any]]:
    """Extract questions from various data formats"""
    questions = []
    
    # Handle different data structures
    if 'questions' in data:
        questions = data['questions']
    elif isinstance(data, list):
        questions = data
    elif 'data' in data:
        questions = data['data']
    
    # Add source metadata to each question
    for q in questions:
        if isinstance(q, dict):
            q['_source_file'] = source_file
            q['_merge_timestamp'] = datetime.now().isoformat()
    
    return questions

def combine_all_propertyguru_data():
    """Combine all PropertyGuru data sources"""
    
    output_dir = "data/processed"
    all_questions = []
    source_files = []
    
    print("🔄 Scanning for all PropertyGuru data files...")
    
    # List of potential data files
    potential_files = [
        "propertyguru_mvp_demo.json",
        "range_1-236_progress.json", 
        "range_237-472_progress.json",
        "range_473-708_progress.json",
        "range_1-2_final.json",
        "comprehensive_progress.json"
    ]
    
    # Also scan for any other PropertyGuru files
    for file in os.listdir(output_dir):
        if 'propertyguru' in file.lower() and file.endswith('.json'):
            if file not in potential_files:
                potential_files.append(file)
    
    print(f"📁 Found potential files: {potential_files}")
    
    # Process each file
    for filename in potential_files:
        file_path = os.path.join(output_dir, filename)
        
        if not os.path.exists(file_path):
            continue
            
        print(f"📊 Processing: {filename}")
        
        data = load_json_file(file_path)
        if not data:
            continue
            
        questions = extract_questions_from_data(data, filename)
        
        if questions:
            all_questions.extend(questions)
            source_files.append({
                'filename': filename,
                'questions_count': len(questions),
                'file_size_mb': round(os.path.getsize(file_path) / (1024*1024), 2)
            })
            print(f"   ✅ Added {len(questions)} questions from {filename}")
        else:
            print(f"   ⚠️  No questions found in {filename}")
    
    # Remove duplicates based on URL (if available)
    print(f"\n🔄 Removing duplicates...")
    unique_questions = []
    seen_urls = set()
    seen_texts = set()
    
    for q in all_questions:
        # Create unique identifier
        url = q.get('url', '')
        text = q.get('text', '')[:100]  # First 100 chars
        
        unique_id = url if url else text
        
        if unique_id and unique_id not in seen_urls and unique_id not in seen_texts:
            unique_questions.append(q)
            if url:
                seen_urls.add(url)
            if text:
                seen_texts.add(text)
    
    print(f"   📉 Removed {len(all_questions) - len(unique_questions)} duplicates")
    
    # Calculate statistics
    total_answers = sum(len(q.get('answers', [])) for q in unique_questions)
    
    # Create comprehensive output
    comprehensive_output = {
        'metadata': {
            'scraper_version': 'comprehensive_all_data_v1.0',
            'source': 'PropertyGuru HDB Questions - ALL Available Data Combined',
            'merge_timestamp': datetime.now().isoformat(),
            'merge_type': 'comprehensive_all_sources',
            'total_source_files': len(source_files),
            'total_questions': len(unique_questions),
            'total_answers': total_answers,
            'avg_answers_per_question': total_answers / len(unique_questions) if unique_questions else 0,
            'source_files_processed': source_files,
            'data_quality_notes': [
                "Combined data from all available PropertyGuru sources",
                "Removed duplicates based on URL and text content",
                "Includes MVP demo data, 3-terminal distributed results, and any other sources",
                "Each question tagged with source file for traceability",
                "Comprehensive answers preserved from all sources"
            ]
        },
        'questions': unique_questions
    }
    
    # Save comprehensive file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    comprehensive_file = os.path.join(output_dir, f"propertyguru_ALL_COMBINED_{timestamp}.json")
    
    with open(comprehensive_file, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_output, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 ALL PROPERTYGURU DATA COMBINED!")
    print(f"📊 Final Statistics:")
    print(f"   • Source files processed: {len(source_files)}")
    print(f"   • Total unique questions: {len(unique_questions):,}")
    print(f"   • Total answers: {total_answers:,}")
    print(f"   • Average answers per question: {total_answers / len(unique_questions) if unique_questions else 0:.1f}")
    print(f"   • Final file size: {round(os.path.getsize(comprehensive_file) / (1024*1024), 2)} MB")
    print(f"   • Comprehensive file: {comprehensive_file}")
    
    print(f"\n📋 Source File Breakdown:")
    for source in source_files:
        print(f"   • {source['filename']}: {source['questions_count']} questions ({source['file_size_mb']} MB)")
    
    return comprehensive_file

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru ALL DATA Combiner                           ║
║                                                                              ║
║  🎯 Combining ALL available PropertyGuru data sources                       ║
║  📊 Creating comprehensive unified dataset                                  ║
║  🔄 Removing duplicates and preserving quality                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    try:
        comprehensive_file = combine_all_propertyguru_data()
        print(f"\n✅ All data combined successfully!")
        print(f"📁 Final output: {comprehensive_file}")
        
    except Exception as e:
        print(f"\n💥 Error during combination: {e}")
        import traceback
        traceback.print_exc()
