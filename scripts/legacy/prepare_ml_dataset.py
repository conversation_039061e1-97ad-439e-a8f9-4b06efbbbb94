#!/usr/bin/env python3
"""
Prepare ML-Ready Dataset from PropertyGuru Data
Aggregates and cleans all useful data for machine learning validation
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any
import re
import statistics

def clean_text(text: str) -> str:
    """Clean and normalize text for ML"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove HTML tags if any
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove URLs from text content (but keep them in metadata)
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    
    # Remove email addresses from text content
    text = re.sub(r'\S+@\S+', '', text)
    
    # Remove phone numbers
    text = re.sub(r'[\+]?[1-9]?[0-9]{7,15}', '', text)
    
    # Clean up extra spaces again
    text = re.sub(r'\s+', ' ', text.strip())
    
    return text

def extract_ml_features(question: Dict[str, Any]) -> Dict[str, Any]:
    """Extract ML-relevant features from question data"""
    
    # Basic question features
    question_text = clean_text(question.get('text', ''))
    question_length = len(question_text)
    question_word_count = len(question_text.split())
    
    # Answer features
    answers = question.get('answers', [])
    answer_count = len(answers)
    
    # Process answers
    answer_texts = []
    answer_lengths = []
    answer_upvotes = []
    
    for answer in answers:
        answer_text = clean_text(answer.get('text', ''))
        answer_texts.append(answer_text)
        answer_lengths.append(len(answer_text))
        
        # Extract upvotes (handle various formats)
        upvotes_str = str(answer.get('upvotes', '0')).strip()
        try:
            upvotes = int(upvotes_str) if upvotes_str.isdigit() else 0
        except:
            upvotes = 0
        answer_upvotes.append(upvotes)
    
    # Aggregate answer statistics
    avg_answer_length = sum(answer_lengths) / len(answer_lengths) if answer_lengths else 0
    total_upvotes = sum(answer_upvotes)
    max_upvotes = max(answer_upvotes) if answer_upvotes else 0
    
    # Combine all answer text for full context
    combined_answers = " ".join(answer_texts)
    
    # Extract metadata
    author = question.get('author', 'Anonymous')
    date_str = question.get('date', '')
    url = question.get('url', '')
    source_file = question.get('_source_file', 'unknown')
    
    # Create ML-ready record
    ml_record = {
        # Core content
        'question_text': question_text,
        'combined_answers': combined_answers,
        'full_context': f"Q: {question_text}\n\nA: {combined_answers}",
        
        # Question features
        'question_length': question_length,
        'question_word_count': question_word_count,
        
        # Answer features
        'answer_count': answer_count,
        'avg_answer_length': avg_answer_length,
        'total_answer_length': sum(answer_lengths),
        'total_upvotes': total_upvotes,
        'max_upvotes': max_upvotes,
        'has_answers': answer_count > 0,
        'has_upvoted_answers': max_upvotes > 0,
        
        # Quality indicators
        'engagement_score': answer_count + total_upvotes,  # Simple engagement metric
        'quality_score': (answer_count * 0.3) + (avg_answer_length * 0.0001) + (total_upvotes * 0.7),
        
        # Metadata
        'author': author,
        'date': date_str,
        'url': url,
        'source_file': source_file,
        'is_anonymous': author.lower() in ['anonymous', ''],
        
        # Raw data for reference
        'raw_answers': answers[:3],  # Keep first 3 answers for reference
        'question_id': url.split('/')[-1] if url else f"q_{hash(question_text) % 100000}"
    }
    
    return ml_record

def prepare_ml_dataset():
    """Prepare comprehensive ML dataset"""
    
    # Load the comprehensive combined data
    combined_file = None
    output_dir = "data/processed"
    
    # Find the most recent combined file
    for file in os.listdir(output_dir):
        if 'propertyguru_ALL_COMBINED' in file and file.endswith('.json'):
            combined_file = os.path.join(output_dir, file)
            break
    
    if not combined_file:
        print("❌ No combined PropertyGuru file found. Please run combine_all_data.py first.")
        return None
    
    print(f"📊 Loading data from: {combined_file}")
    
    with open(combined_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    questions = data.get('questions', [])
    print(f"📈 Processing {len(questions)} questions...")
    
    # Convert to ML-ready format
    ml_records = []
    
    for i, question in enumerate(questions):
        if i % 500 == 0:
            print(f"   Processing question {i+1}/{len(questions)}...")
        
        try:
            ml_record = extract_ml_features(question)
            ml_records.append(ml_record)
        except Exception as e:
            print(f"   ⚠️  Error processing question {i}: {e}")
            continue
    
    print(f"✅ Successfully processed {len(ml_records)} questions")
    
    # Generate dataset statistics without pandas
    questions_with_answers = [r for r in ml_records if r['has_answers']]
    questions_with_upvotes = [r for r in ml_records if r['has_upvoted_answers']]
    anonymous_questions = [r for r in ml_records if r['is_anonymous']]

    question_lengths = [r['question_length'] for r in ml_records]
    answer_counts = [r['answer_count'] for r in ml_records]
    engagement_scores = [r['engagement_score'] for r in ml_records]
    quality_scores = [r['quality_score'] for r in ml_records]
    dates = [r['date'] for r in ml_records if r['date']]

    # Calculate quantile for top quality
    quality_scores_sorted = sorted(quality_scores)
    q80_index = int(len(quality_scores_sorted) * 0.8)
    quality_threshold = quality_scores_sorted[q80_index] if quality_scores_sorted else 0
    top_quality_questions = [r for r in ml_records if r['quality_score'] > quality_threshold]

    stats = {
        'total_questions': len(ml_records),
        'questions_with_answers': len(questions_with_answers),
        'questions_with_upvotes': len(questions_with_upvotes),
        'avg_question_length': statistics.mean(question_lengths) if question_lengths else 0,
        'avg_answer_count': statistics.mean(answer_counts) if answer_counts else 0,
        'avg_engagement_score': statistics.mean(engagement_scores) if engagement_scores else 0,
        'top_quality_questions': len(top_quality_questions),
        'anonymous_questions': len(anonymous_questions),
        'date_range': {
            'earliest': min(dates) if dates else 'N/A',
            'latest': max(dates) if dates else 'N/A'
        }
    }
    
    # Save ML-ready datasets
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 1. Full dataset (JSON)
    ml_dataset = {
        'metadata': {
            'dataset_version': 'ml_ready_v1.0',
            'source': 'PropertyGuru HDB Questions - ML Ready Dataset',
            'created_timestamp': datetime.now().isoformat(),
            'total_records': len(ml_records),
            'features_included': list(ml_records[0].keys()) if ml_records else [],
            'statistics': stats,
            'ml_notes': [
                "Text cleaned and normalized for ML processing",
                "Features extracted for question quality, engagement, and content analysis",
                "Suitable for classification, clustering, and recommendation tasks",
                "Full context preserved for training conversational models",
                "Quality scores calculated for filtering and ranking"
            ]
        },
        'data': ml_records
    }
    
    json_file = os.path.join(output_dir, f"propertyguru_ML_READY_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(ml_dataset, f, indent=2, ensure_ascii=False)
    
    # 2. CSV for easy analysis (manual CSV creation)
    csv_file = os.path.join(output_dir, f"propertyguru_ML_READY_{timestamp}.csv")

    # Create CSV manually
    if ml_records:
        headers = list(ml_records[0].keys())
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Write headers
            f.write(','.join(f'"{h}"' for h in headers) + '\n')
            # Write data
            for record in ml_records:
                row = []
                for header in headers:
                    value = str(record.get(header, '')).replace('"', '""')  # Escape quotes
                    row.append(f'"{value}"')
                f.write(','.join(row) + '\n')

    # 3. High-quality subset (top 20% by quality score)
    hq_csv_file = os.path.join(output_dir, f"propertyguru_HIGH_QUALITY_{timestamp}.csv")

    if top_quality_questions:
        with open(hq_csv_file, 'w', encoding='utf-8') as f:
            headers = list(top_quality_questions[0].keys())
            f.write(','.join(f'"{h}"' for h in headers) + '\n')
            for record in top_quality_questions:
                row = []
                for header in headers:
                    value = str(record.get(header, '')).replace('"', '""')
                    row.append(f'"{value}"')
                f.write(','.join(row) + '\n')
    
    # 4. Training-ready format (question-answer pairs)
    training_pairs = []
    for record in ml_records:
        if record['has_answers']:
            training_pairs.append({
                'input': record['question_text'],
                'output': record['combined_answers'],
                'context': record['full_context'],
                'quality_score': record['quality_score'],
                'engagement_score': record['engagement_score']
            })
    
    training_file = os.path.join(output_dir, f"propertyguru_TRAINING_PAIRS_{timestamp}.json")
    with open(training_file, 'w', encoding='utf-8') as f:
        json.dump(training_pairs, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 ML DATASET PREPARED SUCCESSFULLY!")
    print(f"📊 Dataset Statistics:")
    for key, value in stats.items():
        print(f"   • {key}: {value}")
    
    print(f"\n📁 Output Files:")
    print(f"   • Full ML Dataset (JSON): {json_file}")
    print(f"   • Full Dataset (CSV): {csv_file}")
    print(f"   • High Quality Subset (CSV): {hq_csv_file}")
    print(f"   • Training Pairs (JSON): {training_file}")
    
    print(f"\n🤖 Ready for ML Validation:")
    print(f"   • {len(ml_records)} total samples")
    print(f"   • {len(training_pairs)} question-answer pairs")
    print(f"   • {len(top_quality_questions)} high-quality samples")
    print(f"   • Features: question analysis, answer quality, engagement metrics")
    
    return {
        'json_file': json_file,
        'csv_file': csv_file,
        'hq_csv_file': hq_csv_file,
        'training_file': training_file,
        'stats': stats
    }

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru ML Dataset Preparation                      ║
║                                                                              ║
║  🎯 Preparing clean, ML-ready dataset for validation                       ║
║  🤖 Extracting features for machine learning models                        ║
║  📊 Creating training pairs and quality metrics                             ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    try:
        result = prepare_ml_dataset()
        if result:
            print(f"\n✅ Dataset preparation completed!")
            print(f"🚀 Ready for machine learning validation!")
        
    except Exception as e:
        print(f"\n💥 Error during dataset preparation: {e}")
        import traceback
        traceback.print_exc()
