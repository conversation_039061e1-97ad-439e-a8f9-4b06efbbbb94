#!/usr/bin/env python3
"""
Simple All Pages Scraper - Uses the EXACT working demo method
"""

import asyncio
import json
import os
import sys
from datetime import datetime

# Add the project root to Python path
sys.path.append('/Users/<USER>/Desktop/VS code file/MVP for Pearl<PERSON>')

async def scrape_single_page(page_num):
    """Scrape a single page using the exact working demo method"""
    
    # Import here to avoid import issues
    from mvp_scraper.property_guru_scraper import scrape_property_guru
    
    print(f"📄 Scraping page {page_num}...")
    
    try:
        # This is the EXACT same call that works in demo_presentation.py
        questions = await scrape_property_guru(max_questions=None)  # Get all questions on the page
        
        if questions:
            print(f"   ✅ Success: {len(questions)} questions from page {page_num}")
            return questions
        else:
            print(f"   ❌ No data from page {page_num}")
            return []
            
    except Exception as e:
        print(f"   ❌ Error on page {page_num}: {e}")
        return []

async def main():
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Simple PropertyGuru All Pages Scraper                    ║
║                                                                              ║
║  Uses the EXACT same working method from your successful demo               ║
║  Will scrape all 708 pages one by one                                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Create output directory
    output_dir = "data/processed"
    os.makedirs(output_dir, exist_ok=True)
    
    all_questions = []
    failed_pages = []
    
    start_time = datetime.now()
    print(f"🚀 Starting at {start_time}")
    
    # TEST: Just pages 1-3 first
    for page in range(1, 4):  # Change to range(1, 709) for full run
        try:
            questions = await scrape_single_page(page)
            
            if questions:
                all_questions.extend(questions)
                print(f"   📈 Total questions so far: {len(all_questions)}")
                
                # Save progress every page for now
                progress_file = os.path.join(output_dir, f"progress_page_{page}.json")
                with open(progress_file, 'w') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'last_page': page,
                        'total_questions': len(all_questions),
                        'questions': all_questions
                    }, f, indent=2)
                print(f"   💾 Progress saved to {progress_file}")
            else:
                failed_pages.append(page)
            
            # Polite delay
            await asyncio.sleep(3)
            
        except KeyboardInterrupt:
            print(f"\n⏸️  Interrupted at page {page}")
            break
        except Exception as e:
            print(f"❌ Critical error on page {page}: {e}")
            failed_pages.append(page)
            continue
    
    # Final summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    print(f"\n🎉 COMPLETED!")
    print(f"📊 Results:")
    print(f"   • Total questions: {len(all_questions)}")
    print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in all_questions)}")
    print(f"   • Duration: {duration}")
    print(f"   • Failed pages: {failed_pages}")
    
    # Save final file
    final_file = os.path.join(output_dir, f"propertyguru_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(final_file, 'w') as f:
        json.dump({
            'metadata': {
                'scraping_date': datetime.now().isoformat(),
                'total_questions': len(all_questions),
                'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                'duration': str(duration),
                'failed_pages': failed_pages
            },
            'questions': all_questions
        }, f, indent=2)
    
    print(f"   • Final file: {final_file}")

if __name__ == "__main__":
    asyncio.run(main())
