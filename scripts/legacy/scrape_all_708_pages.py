#!/usr/bin/env python3
"""
Scrape All 708 PropertyGuru Pages
Uses the EXACT same working method from demo_presentation.py
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from mvp_scraper.property_guru_scraper import scrape_property_guru

async def scrape_single_page(page_num):
    """Scrape a single PropertyGuru page"""
    
    # Temporarily modify the URL to point to specific page
    from mvp_scraper.property_guru_scraper import PropertyGuruScraper
    
    scraper = PropertyGuruScraper()
    
    # Modify the base URL for this specific page
    original_base_url = scraper.base_url
    scraper.base_url = f"https://www.propertyguru.com.sg/property-investment-questions/hdb-questions/{page_num}"
    
    try:
        # Use the exact same method as demo - no limits
        questions = await scraper.scrape_property_guru(max_questions=None)
        return questions
    finally:
        # Restore original URL
        scraper.base_url = original_base_url

async def main():
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru ALL 708 PAGES Scraper                       ║
║                                                                              ║
║  Using the EXACT same working method from demo_presentation.py              ║
║  Will scrape all 708 pages (~14,000+ questions with answers)               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Create output directory
    output_dir = "data/processed"
    os.makedirs(output_dir, exist_ok=True)
    
    all_questions = []
    failed_pages = []
    
    start_time = datetime.now()
    print(f"🚀 Starting comprehensive scraping at {start_time}")
    print(f"📊 Target: 708 pages")
    print(f"⏱️  Estimated time: 12-24 hours")
    print()
    
    for page in range(1, 709):  # Pages 1 to 708
        try:
            print(f"📄 Scraping page {page}/708 ({(page/708)*100:.1f}% complete)")
            
            questions = await scrape_single_page(page)
            
            if questions:
                all_questions.extend(questions)
                total_answers = sum(len(q.get('answers', [])) for q in questions)
                print(f"   ✅ Success: {len(questions)} questions, {total_answers} answers")
                print(f"   📈 Total so far: {len(all_questions)} questions, {sum(len(q.get('answers', [])) for q in all_questions)} answers")
            else:
                failed_pages.append(page)
                print(f"   ❌ Failed: No data from page {page}")
            
            # Save progress every 10 pages
            if page % 10 == 0:
                progress_file = os.path.join(output_dir, f"propertyguru_progress_page_{page}.json")
                with open(progress_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'last_completed_page': page,
                        'total_questions': len(all_questions),
                        'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                        'failed_pages': failed_pages,
                        'questions': all_questions
                    }, f, indent=2, ensure_ascii=False)
                print(f"   💾 Progress saved: {len(all_questions)} questions")
            
            # Polite delay between pages
            await asyncio.sleep(3)
            
        except KeyboardInterrupt:
            print(f"\n⏸️  Scraping interrupted by user at page {page}")
            break
            
        except Exception as e:
            print(f"   ❌ Error on page {page}: {e}")
            failed_pages.append(page)
            continue
    
    # Final save
    end_time = datetime.now()
    duration = end_time - start_time
    
    final_output = {
        'metadata': {
            'scraping_start': start_time.isoformat(),
            'scraping_end': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'total_pages_attempted': page,
            'total_questions': len(all_questions),
            'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
            'failed_pages_count': len(failed_pages),
            'success_rate': ((page - len(failed_pages)) / page) * 100 if page > 0 else 0
        },
        'failed_pages': failed_pages,
        'questions': all_questions
    }
    
    # Save final comprehensive file
    final_file = os.path.join(output_dir, f"propertyguru_all_708_pages_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(final_file, 'w', encoding='utf-8') as f:
        json.dump(final_output, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 SCRAPING COMPLETED!")
    print(f"📊 Final Statistics:")
    print(f"   • Total questions: {len(all_questions):,}")
    print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
    print(f"   • Duration: {duration}")
    print(f"   • Failed pages: {len(failed_pages)}")
    print(f"   • Success rate: {((page - len(failed_pages)) / page) * 100:.1f}%")
    print(f"   • Output file: {final_file}")
    
    if failed_pages:
        print(f"   • Failed pages: {failed_pages[:20]}{'...' if len(failed_pages) > 20 else ''}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Scraping stopped by user. Check progress files in data/processed/")
    except Exception as e:
        print(f"\n💥 Critical error: {e}")
        print("Check the progress files in data/processed/ to see what was scraped")
