#!/usr/bin/env python3
"""
Codebase Cleanup Script
Organizes files, removes duplicates, and creates a clean project structure
"""

import os
import shutil
from datetime import datetime
import json

def cleanup_codebase():
    """Clean up the entire codebase"""
    
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                           CODEBASE CLEANUP                                  ║
║                                                                              ║
║  🧹 Organizing files and removing unnecessary duplicates                    ║
║  📁 Creating clean project structure                                        ║
║  🗑️  Moving legacy files to archive                                         ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    # Create organized directory structure
    directories_to_create = [
        "scrapers",
        "data/final",
        "data/archive", 
        "scripts/legacy",
        "scripts/utilities",
        "docs"
    ]
    
    print("📁 Creating organized directory structure...")
    for directory in directories_to_create:
        os.makedirs(directory, exist_ok=True)
        print(f"   ✅ Created: {directory}")
    
    # Files to keep in main directory (core files)
    core_files = {
        "PROJECT_PLAN.md": "docs/",
        "README.md": ".",
        "requirements.txt": ".",
        "demo_presentation.py": "scripts/utilities/"
    }
    
    # Scraper files to organize
    scraper_files = {
        "distributed_propertyguru_scraper.py": "scrapers/",
        "comprehensive_propertyguru_scraper.py": "scrapers/",
        "parallel_propertyguru_scraper.py": "scrapers/",
        "start_distributed_scraping.sh": "scrapers/"
    }
    
    # Legacy/utility files to archive
    legacy_files = {
        "combine_all_data.py": "scripts/legacy/",
        "fix_csv_format.py": "scripts/legacy/",
        "merge_partial_ranges.py": "scripts/legacy/",
        "prepare_ml_dataset.py": "scripts/legacy/",
        "monitor_scraping_progress.py": "scripts/legacy/",
        "scrape_all_708_pages.py": "scripts/legacy/",
        "scrape_all_propertyguru_pages.py": "scripts/legacy/",
        "simple_all_pages_scraper.py": "scripts/legacy/",
        "cleanup_codebase.py": "scripts/utilities/"
    }
    
    # Move core files
    print("\n📋 Organizing core files...")
    for file, destination in core_files.items():
        if os.path.exists(file):
            if destination != ".":
                shutil.move(file, os.path.join(destination, file))
                print(f"   📄 Moved {file} → {destination}")
            else:
                print(f"   📄 Kept {file} in root")
    
    # Move scraper files
    print("\n🕷️  Organizing scraper files...")
    for file, destination in scraper_files.items():
        if os.path.exists(file):
            shutil.move(file, os.path.join(destination, file))
            print(f"   🔧 Moved {file} → {destination}")
    
    # Move legacy files
    print("\n🗃️  Archiving legacy files...")
    for file, destination in legacy_files.items():
        if os.path.exists(file):
            shutil.move(file, os.path.join(destination, file))
            print(f"   📦 Archived {file} → {destination}")
    
    # Clean up data directory
    print("\n🗂️  Organizing data files...")
    
    # Final ML-ready files to keep
    final_files = [
        "propertyguru_ML_PROPER_20250716_042751.csv",
        "propertyguru_HIGH_QUALITY_PROPER_20250716_042751.csv", 
        "propertyguru_ALL_COMBINED_20250715_135521.json",
        "propertyguru_TRAINING_PAIRS_20250716_042457.json"
    ]
    
    # Move final files
    for file in final_files:
        src = os.path.join("data/processed", file)
        if os.path.exists(src):
            shutil.move(src, os.path.join("data/final", file))
            print(f"   📊 Final dataset: {file} → data/final/")
    
    # Archive remaining processed files
    processed_files = os.listdir("data/processed")
    for file in processed_files:
        src = os.path.join("data/processed", file)
        if os.path.isfile(src):
            shutil.move(src, os.path.join("data/archive", file))
            print(f"   🗄️  Archived: {file} → data/archive/")
    
    # Remove empty processed directory
    if os.path.exists("data/processed") and not os.listdir("data/processed"):
        os.rmdir("data/processed")
        print("   🗑️  Removed empty data/processed directory")
    
    # Clean up any remaining loose files
    loose_files = ["bto_processed_reviews.json"]
    print("\n🧹 Cleaning up loose files...")
    for file in loose_files:
        if os.path.exists(file):
            shutil.move(file, os.path.join("data/archive", file))
            print(f"   🗄️  Archived: {file} → data/archive/")
    
    # Create project summary
    create_project_summary()
    
    print(f"\n🎉 CODEBASE CLEANUP COMPLETED!")
    print(f"📁 New Structure:")
    print(f"   • scrapers/ - Main scraping tools")
    print(f"   • data/final/ - Final ML-ready datasets")
    print(f"   • data/archive/ - Historical data and progress files")
    print(f"   • scripts/legacy/ - Development and utility scripts")
    print(f"   • scripts/utilities/ - Helper tools")
    print(f"   • docs/ - Documentation")
    print(f"   • mvp_scraper/ - Core scraper package")
    print(f"   • venv/ - Virtual environment")

def create_project_summary():
    """Create a comprehensive project summary"""
    
    summary = {
        "project": "PropertyGuru HDB Questions Scraper",
        "description": "Comprehensive scraping and ML dataset preparation for PropertyGuru HDB questions and answers",
        "created": datetime.now().isoformat(),
        "structure": {
            "scrapers/": {
                "description": "Main scraping tools",
                "files": {
                    "distributed_propertyguru_scraper.py": "3-terminal distributed scraper (RECOMMENDED)",
                    "comprehensive_propertyguru_scraper.py": "Sequential comprehensive scraper",
                    "parallel_propertyguru_scraper.py": "Multi-threaded parallel scraper",
                    "start_distributed_scraping.sh": "Auto-launch script for distributed scraping"
                }
            },
            "data/final/": {
                "description": "Final ML-ready datasets",
                "files": {
                    "propertyguru_ML_PROPER_20250716_042751.csv": "Main ML dataset (2,400 questions, proper CSV format)",
                    "propertyguru_HIGH_QUALITY_PROPER_20250716_042751.csv": "High-quality subset (479 questions)",
                    "propertyguru_ALL_COMBINED_20250715_135521.json": "Complete JSON dataset with metadata",
                    "propertyguru_TRAINING_PAIRS_20250716_042457.json": "Question-answer pairs for training"
                }
            },
            "mvp_scraper/": {
                "description": "Core scraper package",
                "files": {
                    "property_guru_scraper.py": "PropertyGuru scraping logic",
                    "reddit_scraper.py": "Reddit scraping logic",
                    "hardware_zone_scraper.py": "HardwareZone scraping logic",
                    "scraper_helper.py": "Common scraping utilities",
                    "gemini_processor.py": "AI processing utilities"
                }
            },
            "scripts/": {
                "description": "Utility and legacy scripts",
                "legacy/": "Development scripts used during project",
                "utilities/": "Helper tools and demos"
            },
            "docs/": {
                "description": "Project documentation",
                "files": {
                    "PROJECT_PLAN.md": "Original project plan and requirements"
                }
            }
        },
        "dataset_stats": {
            "total_questions": 2400,
            "questions_with_answers": 2056,
            "high_quality_questions": 479,
            "date_range": "April 2013 - September 2024",
            "avg_answers_per_question": 3.0,
            "csv_columns": 27,
            "max_answers_per_question": 3
        },
        "usage": {
            "recommended_scraper": "scrapers/distributed_propertyguru_scraper.py",
            "main_dataset": "data/final/propertyguru_ML_PROPER_20250716_042751.csv",
            "high_quality_subset": "data/final/propertyguru_HIGH_QUALITY_PROPER_20250716_042751.csv",
            "training_pairs": "data/final/propertyguru_TRAINING_PAIRS_20250716_042457.json"
        },
        "next_steps": [
            "Use data/final/ datasets for ML validation",
            "Test Reddit scraping with similar approach",
            "Implement ML models for quality prediction",
            "Create recommendation system using the data"
        ]
    }
    
    with open("PROJECT_SUMMARY.json", "w", encoding="utf-8") as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print("   📋 Created PROJECT_SUMMARY.json")

if __name__ == "__main__":
    try:
        cleanup_codebase()
        print("\n✅ Codebase cleanup completed successfully!")
        print("🚀 Project is now organized and ready for ML validation!")
        
    except Exception as e:
        print(f"\n💥 Error during cleanup: {e}")
        import traceback
        traceback.print_exc()
