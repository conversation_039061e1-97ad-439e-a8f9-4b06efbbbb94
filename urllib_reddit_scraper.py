#!/usr/bin/env python3
"""
Reddit Scraper using urllib (built-in Python module)
"""

import urllib.request
import urllib.parse
import json
import os
import re
from datetime import datetime
import time

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove common HTML entities
    text = re.sub(r'&amp;', '&', text)
    text = re.sub(r'&lt;', '<', text)
    text = re.sub(r'&gt;', '>', text)
    text = re.sub(r'&quot;', '"', text)
    text = re.sub(r'&#x27;', "'", text)
    
    return text.strip()

def scrape_reddit_url(url):
    """Scrape Reddit URL using urllib"""
    try:
        print(f"🔄 Scraping: {url}")
        
        # Create request with headers
        req = urllib.request.Request(
            url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            }
        )
        
        # Make request
        with urllib.request.urlopen(req, timeout=30) as response:
            html = response.read().decode('utf-8')
        
        # Extract title
        title_patterns = [
            r'<title[^>]*>([^<]+)</title>',
            r'"title":"([^"]+)"',
            r'<h1[^>]*>([^<]+)</h1>'
        ]
        
        title = "Unknown Title"
        for pattern in title_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                title = clean_text(match.group(1))
                if title and len(title) > 5 and "reddit" not in title.lower():
                    break
        
        # Extract post content
        post_patterns = [
            r'"selftext":"([^"]+)"',
            r'"text":"([^"]+)"',
            r'<div[^>]*data-click-id="text"[^>]*>([^<]+)</div>'
        ]
        
        post_text = ""
        for pattern in post_patterns:
            match = re.search(pattern, html, re.IGNORECASE | re.DOTALL)
            if match:
                post_text = clean_text(match.group(1))
                if post_text and len(post_text) > 10:
                    break
        
        # If no post text, use title
        if not post_text or len(post_text) < 10:
            post_text = title
        
        # Extract comments using multiple patterns
        comments = []
        
        # Pattern 1: JSON format comments
        json_pattern = r'"body":"([^"]{30,})"'
        json_matches = re.findall(json_pattern, html, re.IGNORECASE)
        
        for match in json_matches[:20]:  # Limit to 20 comments
            comment_text = clean_text(match)
            if comment_text and len(comment_text) > 30:
                comments.append({
                    'text': comment_text,
                    'author': f'User_{len(comments)+1}',
                    'date': datetime.now().isoformat()
                })
        
        # Pattern 2: HTML format comments (if JSON didn't work)
        if not comments:
            html_pattern = r'<div[^>]*class="[^"]*comment[^"]*"[^>]*>([^<]{30,})</div>'
            html_matches = re.findall(html_pattern, html, re.IGNORECASE | re.DOTALL)
            
            for match in html_matches[:20]:
                comment_text = clean_text(match)
                if comment_text and len(comment_text) > 30:
                    comments.append({
                        'text': comment_text,
                        'author': f'User_{len(comments)+1}',
                        'date': datetime.now().isoformat()
                    })
        
        # Extract subreddit from URL
        subreddit = "unknown"
        if "/r/" in url:
            subreddit = url.split("/r/")[1].split("/")[0]
        
        result = {
            'url': url,
            'title': title,
            'question': post_text,
            'subreddit': subreddit,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        print(f"✅ Success: {len(comments)} comments found")
        return result
        
    except Exception as e:
        print(f"💥 Error: {e}")
        return None

def convert_to_csv_format(reddit_data):
    """Convert to CSV format"""
    csv_rows = []
    
    for post in reddit_data:
        if not post or not post.get('comments'):
            continue
        
        question = post['question']
        comments = post['comments']
        
        if not question or not comments:
            continue
        
        # Create CSV row
        row = {'question': question}
        
        # Add answers (unlimited)
        for i, comment in enumerate(comments, 1):
            row[f'answer{i}'] = comment['text']
        
        csv_rows.append(row)
        print(f"   📊 Question with {len(comments)} answers")
    
    return csv_rows

def main():
    """Main function"""
    urls = [
        "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
        "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
        "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
        "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
        "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
        "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
        "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/"
    ]
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    urllib Reddit Scraper                                    ║
║                                                                              ║
║  🎯 Target: {len(urls)} Singapore housing/BTO Reddit posts                          ║
║  📊 Output: question, answer1, answer2, ... (unlimited)                     ║
║  🔧 Method: Built-in urllib (no external dependencies)                      ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    all_data = []
    successful = 0
    
    for i, url in enumerate(urls, 1):
        print(f"\n🔄 Processing {i}/{len(urls)}")
        
        result = scrape_reddit_url(url)
        if result and result.get('comments'):
            all_data.append(result)
            successful += 1
        
        # Polite delay
        time.sleep(3)
    
    print(f"\n📊 Scraping completed: {successful}/{len(urls)} successful")
    
    if not all_data:
        print("❌ No data collected. Reddit may be blocking requests or using anti-bot measures.")
        return None
    
    # Convert to CSV format
    print(f"\n🔄 Converting to CSV format...")
    csv_data = convert_to_csv_format(all_data)
    
    # Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_urllib_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_urllib_{timestamp}.csv"
    if csv_data:
        # Find all answer columns
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Headers
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            # Data
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    
    print(f"\n🎉 Reddit scraping completed!")
    print(f"📊 Statistics:")
    print(f"   • Questions: {total_questions}")
    print(f"   • Answers: {total_answers}")
    print(f"   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}")
    
    print(f"\n📁 Files saved:")
    print(f"   • Raw data: {raw_file}")
    print(f"   • CSV format: {csv_file}")
    
    print(f"\n🚀 Next: Combine with PropertyGuru data for ML validation!")
    
    return csv_file

if __name__ == "__main__":
    main()
