#!/usr/bin/env python3
"""
PropertyGuru Scraping Progress Monitor
Real-time monitoring of scraping progress with statistics and estimates
"""

import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any

class ScrapingMonitor:
    def __init__(self, progress_file: str = "data/processed/propertyguru_progress.json"):
        self.progress_file = progress_file
        self.total_pages = 708
        
    def get_progress_data(self) -> Dict[str, Any]:
        """Load current progress data"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading progress: {e}")
        
        return {}
    
    def calculate_statistics(self, progress: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive statistics"""
        if not progress:
            return {"error": "No progress data available"}
        
        last_page = progress.get('last_completed_page', 0)
        scraped_data = progress.get('scraped_data', [])
        failed_pages = progress.get('failed_pages', [])
        timestamp = progress.get('timestamp', '')
        
        # Basic stats
        total_questions = len(scraped_data)
        total_answers = sum(len(q.get('answers', [])) for q in scraped_data)
        completion_percentage = (last_page / self.total_pages) * 100
        
        # Time calculations
        stats = {
            'pages_completed': last_page,
            'pages_remaining': self.total_pages - last_page,
            'total_pages': self.total_pages,
            'completion_percentage': completion_percentage,
            'total_questions': total_questions,
            'total_answers': total_answers,
            'failed_pages_count': len(failed_pages),
            'last_update': timestamp
        }
        
        # Calculate rates and estimates if we have data
        if last_page > 0 and timestamp:
            try:
                last_update = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                now = datetime.now()
                
                # Estimate time per page (rough calculation)
                if last_page > 10:  # Only calculate if we have enough data
                    avg_questions_per_page = total_questions / last_page
                    avg_answers_per_question = total_answers / total_questions if total_questions > 0 else 0
                    
                    stats.update({
                        'avg_questions_per_page': round(avg_questions_per_page, 1),
                        'avg_answers_per_question': round(avg_answers_per_question, 1),
                        'estimated_total_questions': round(avg_questions_per_page * self.total_pages),
                        'estimated_total_answers': round(avg_questions_per_page * avg_answers_per_question * self.total_pages)
                    })
                
            except Exception as e:
                stats['time_calculation_error'] = str(e)
        
        return stats
    
    def display_progress(self):
        """Display current progress in a formatted way"""
        progress = self.get_progress_data()
        stats = self.calculate_statistics(progress)
        
        if "error" in stats:
            print(f"❌ {stats['error']}")
            return
        
        print("\n" + "="*80)
        print("🔍 PROPERTYGURU SCRAPING PROGRESS MONITOR")
        print("="*80)
        
        # Progress bar
        completion = stats['completion_percentage']
        bar_length = 50
        filled_length = int(bar_length * completion / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        print(f"\n📊 OVERALL PROGRESS")
        print(f"   {bar} {completion:.1f}%")
        print(f"   Pages: {stats['pages_completed']}/{stats['total_pages']} "
              f"({stats['pages_remaining']} remaining)")
        
        print(f"\n📈 DATA COLLECTED")
        print(f"   Questions scraped: {stats['total_questions']:,}")
        print(f"   Answers scraped: {stats['total_answers']:,}")
        
        if stats.get('avg_questions_per_page'):
            print(f"   Avg questions/page: {stats['avg_questions_per_page']}")
            print(f"   Avg answers/question: {stats['avg_answers_per_question']}")
        
        if stats.get('estimated_total_questions'):
            print(f"\n🎯 PROJECTIONS")
            print(f"   Estimated total questions: {stats['estimated_total_questions']:,}")
            print(f"   Estimated total answers: {stats['estimated_total_answers']:,}")
        
        if stats['failed_pages_count'] > 0:
            print(f"\n⚠️  ISSUES")
            print(f"   Failed pages: {stats['failed_pages_count']}")
        
        print(f"\n🕐 LAST UPDATE")
        print(f"   {stats['last_update']}")
        
        print("="*80)
    
    def monitor_continuously(self, interval: int = 30):
        """Monitor progress continuously with updates every interval seconds"""
        print(f"🔄 Starting continuous monitoring (updates every {interval} seconds)")
        print("   Press Ctrl+C to stop")
        
        try:
            while True:
                os.system('clear' if os.name == 'posix' else 'cls')  # Clear screen
                self.display_progress()
                print(f"\n⏰ Next update in {interval} seconds...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n\n👋 Monitoring stopped by user")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Monitor PropertyGuru scraping progress')
    parser.add_argument('--continuous', '-c', action='store_true',
                       help='Monitor continuously with regular updates')
    parser.add_argument('--interval', '-i', type=int, default=30,
                       help='Update interval in seconds for continuous monitoring (default: 30)')
    
    args = parser.parse_args()
    
    monitor = ScrapingMonitor()
    
    if args.continuous:
        monitor.monitor_continuously(args.interval)
    else:
        monitor.display_progress()

if __name__ == "__main__":
    main()
