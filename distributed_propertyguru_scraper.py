#!/usr/bin/env python3
"""
Distributed PropertyGuru Scraper - Manual Terminal Distribution
Run different page ranges in separate terminals for maximum parallelism

Usage Examples:
Terminal 1: python distributed_propertyguru_scraper.py --range 1-100
Terminal 2: python distributed_propertyguru_scraper.py --range 101-200
Terminal 3: python distributed_propertyguru_scraper.py --range 201-300
...and so on

Features:
- Each terminal handles a specific page range
- Independent progress tracking per range
- Automatic result merging
- Resume capability per range
- No conflicts between terminals
"""

import asyncio
import json
import os
import sys
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from mvp_scraper.property_guru_scraper import PropertyGuruScraper

class DistributedPropertyGuruScraper:
    def __init__(self, start_page: int, end_page: int, output_dir: str = "data/processed"):
        self.start_page = start_page
        self.end_page = end_page
        self.range_id = f"{start_page}-{end_page}"
        self.output_dir = output_dir
        self.progress_file = os.path.join(output_dir, f"range_{self.range_id}_progress.json")
        self.final_file = os.path.join(output_dir, f"range_{self.range_id}_final.json")
        self.max_retries = 3
        self.retry_delay = 5.0
        self.page_delay = 2.0
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Statistics tracking
        self.stats = {
            'range_id': self.range_id,
            'start_page': start_page,
            'end_page': end_page,
            'total_pages': end_page - start_page + 1,
            'start_time': None,
            'last_update': None,
            'pages_completed': 0,
            'pages_failed': [],
            'total_questions': 0,
            'total_answers': 0
        }

    async def scrape_single_page_with_retry(self, page_num: int) -> List[Dict[str, Any]]:
        """Scrape a single page with retry logic"""
        
        for attempt in range(self.max_retries):
            try:
                print(f"   📄 Page {page_num}, attempt {attempt + 1}/{self.max_retries}")
                
                # Create scraper instance
                scraper = PropertyGuruScraper()
                
                # Modify URL for specific page
                original_url = scraper.base_url
                scraper.base_url = f"https://www.propertyguru.com.sg/property-investment-questions/hdb-questions/{page_num}"
                
                try:
                    # Scrape with NO LIMITS - get all questions on the page
                    questions = await scraper.scrape_property_guru(max_questions=None)
                    
                    if questions:
                        total_answers = sum(len(q.get('answers', [])) for q in questions)
                        print(f"   ✅ Success: {len(questions)} questions, {total_answers} answers")
                        return questions
                    else:
                        print(f"   ⚠️  No data found on attempt {attempt + 1}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                        else:
                            return []
                            
                finally:
                    # Restore original URL
                    scraper.base_url = original_url
                    
            except Exception as e:
                print(f"   ❌ Error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    print(f"   💥 All retry attempts failed for page {page_num}")
                    return []
        
        return []

    def load_progress(self) -> Dict[str, Any]:
        """Load previous scraping progress for this range"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)
                    print(f"📂 Loaded progress for range {self.range_id}: "
                          f"page {progress.get('last_completed_page', self.start_page - 1)}, "
                          f"{len(progress.get('questions', []))} questions")
                    return progress
        except Exception as e:
            print(f"⚠️  Error loading progress: {e}")
        
        return {
            'range_id': self.range_id,
            'last_completed_page': self.start_page - 1,
            'questions': [],
            'failed_pages': [],
            'stats': {}
        }

    def save_progress(self, current_page: int, all_questions: List[Dict[str, Any]], 
                     failed_pages: List[int]):
        """Save current progress for this range"""
        try:
            # Update statistics
            self.stats.update({
                'last_update': datetime.now().isoformat(),
                'pages_completed': current_page - self.start_page + 1,
                'total_questions': len(all_questions),
                'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                'pages_failed': failed_pages,
                'completion_percentage': ((current_page - self.start_page + 1) / self.stats['total_pages']) * 100
            })
            
            progress = {
                'range_id': self.range_id,
                'last_completed_page': current_page,
                'questions': all_questions,
                'failed_pages': failed_pages,
                'stats': self.stats,
                'timestamp': datetime.now().isoformat()
            }
            
            # Save progress file
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"⚠️  Error saving progress: {e}")

    def display_progress_stats(self, current_page: int, all_questions: List[Dict[str, Any]]):
        """Display current progress statistics for this range"""
        if self.stats['start_time']:
            elapsed = datetime.now() - datetime.fromisoformat(self.stats['start_time'])
            pages_done = current_page - self.start_page + 1
            completion_pct = (pages_done / self.stats['total_pages']) * 100
            
            print(f"\n📊 Range {self.range_id} Progress:")
            print(f"   • Pages: {pages_done}/{self.stats['total_pages']} ({completion_pct:.1f}%)")
            print(f"   • Current page: {current_page}")
            print(f"   • Questions: {len(all_questions):,}")
            print(f"   • Answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
            print(f"   • Elapsed: {elapsed}")
            
            if pages_done > 0:
                print(f"   • Avg time/page: {elapsed.total_seconds()/pages_done:.1f}s")
                
                # ETA calculation
                remaining_pages = self.stats['total_pages'] - pages_done
                if remaining_pages > 0:
                    eta_seconds = (elapsed.total_seconds() / pages_done) * remaining_pages
                    eta = datetime.now() + timedelta(seconds=eta_seconds)
                    print(f"   • ETA: {eta.strftime('%H:%M:%S')}")
            print()

    async def scrape_page_range(self, resume: bool = True) -> List[Dict[str, Any]]:
        """Scrape the assigned page range"""
        
        print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    DISTRIBUTED PropertyGuru Scraper                         ║
║                                                                              ║
║  📍 Range: Pages {self.start_page}-{self.end_page} ({self.stats['total_pages']} pages)                                    ║
║                                                                              ║
║  🚀 DISTRIBUTED FEATURES:                                                    ║
║  ✅ Independent range processing                                            ║
║  ✅ No conflicts with other terminals                                       ║
║  ✅ Resume capability per range                                             ║
║  ✅ Individual progress tracking                                            ║
║  ✅ No testing limits - scrapes ALL questions per page                      ║
║                                                                              ║
║  💡 Run multiple terminals for maximum speed!                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)
        
        # Load previous progress if resuming
        if resume:
            progress = self.load_progress()
            start_from = progress.get('last_completed_page', self.start_page - 1) + 1
            all_questions = progress.get('questions', [])
            failed_pages = progress.get('failed_pages', [])
            self.stats = {**self.stats, **progress.get('stats', {})}
        else:
            start_from = self.start_page
            all_questions = []
            failed_pages = []
        
        # Set start time if not resuming
        if not self.stats.get('start_time'):
            self.stats['start_time'] = datetime.now().isoformat()
        
        print(f"🚀 Range {self.range_id}: Starting from page {start_from}")
        print(f"📈 Already have {len(all_questions)} questions")
        print(f"🎯 Target: Pages {self.start_page} to {self.end_page}")
        print()
        
        try:
            for page in range(start_from, self.end_page + 1):
                print(f"🔄 Processing page {page}/{self.end_page} (Range {self.range_id})")
                
                try:
                    questions = await self.scrape_single_page_with_retry(page)
                    
                    if questions:
                        all_questions.extend(questions)
                        print(f"   📈 Range total: {len(all_questions)} questions, "
                              f"{sum(len(q.get('answers', [])) for q in all_questions)} answers")
                    else:
                        failed_pages.append(page)
                        print(f"   ❌ Page {page} failed after all retries")
                    
                    # Save progress every 10 pages
                    if (page - self.start_page + 1) % 10 == 0:
                        self.save_progress(page, all_questions, failed_pages)
                        self.display_progress_stats(page, all_questions)
                    
                    # Polite delay between pages
                    await asyncio.sleep(self.page_delay)
                    
                except KeyboardInterrupt:
                    print(f"\n⏸️  Range {self.range_id} interrupted at page {page}")
                    self.save_progress(page - 1, all_questions, failed_pages)
                    raise
                    
                except Exception as e:
                    print(f"   💥 Critical error on page {page}: {e}")
                    failed_pages.append(page)
                    continue
            
            # Final save for this range
            self.save_progress(self.end_page, all_questions, failed_pages)
            
        except KeyboardInterrupt:
            print(f"\n⏸️  Range {self.range_id} scraping interrupted by user")
            print("📁 Progress has been saved and can be resumed later")
            return all_questions
        
        # Save final output for this range
        await self.save_final_range_output(all_questions, failed_pages)
        
        return all_questions

    async def save_final_range_output(self, all_questions: List[Dict[str, Any]], failed_pages: List[int]):
        """Save final output for this range"""
        try:
            end_time = datetime.now()
            start_time = datetime.fromisoformat(self.stats['start_time'])
            duration = end_time - start_time

            range_output = {
                'metadata': {
                    'scraper_version': 'distributed_v1.0',
                    'range_id': self.range_id,
                    'start_page': self.start_page,
                    'end_page': self.end_page,
                    'total_pages_in_range': self.stats['total_pages'],
                    'scraping_start': self.stats['start_time'],
                    'scraping_end': end_time.isoformat(),
                    'duration': str(duration),
                    'duration_seconds': duration.total_seconds(),
                    'total_questions': len(all_questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                    'failed_pages_count': len(failed_pages),
                    'success_rate': ((self.stats['total_pages'] - len(failed_pages)) / self.stats['total_pages']) * 100,
                    'avg_questions_per_page': len(all_questions) / self.stats['total_pages'] if len(all_questions) > 0 else 0
                },
                'failed_pages': failed_pages,
                'questions': all_questions
            }

            # Save final range file
            with open(self.final_file, 'w', encoding='utf-8') as f:
                json.dump(range_output, f, indent=2, ensure_ascii=False)

            print(f"\n🎉 RANGE {self.range_id} COMPLETED!")
            print(f"📊 Range Statistics:")
            print(f"   • Pages processed: {self.start_page}-{self.end_page}")
            print(f"   • Total questions: {len(all_questions):,}")
            print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
            print(f"   • Duration: {duration}")
            print(f"   • Failed pages: {len(failed_pages)}")
            print(f"   • Success rate: {((self.stats['total_pages'] - len(failed_pages)) / self.stats['total_pages']) * 100:.1f}%")
            print(f"   • Range output: {self.final_file}")

            if failed_pages:
                print(f"   • Failed pages: {failed_pages}")

        except Exception as e:
            print(f"⚠️  Error saving final range output: {e}")

    @staticmethod
    def merge_all_ranges(output_dir: str = "data/processed") -> str:
        """Merge all completed ranges into a single comprehensive file"""
        try:
            print("🔄 Merging all completed ranges...")

            # Find all range files
            range_files = [f for f in os.listdir(output_dir) if f.startswith('range_') and f.endswith('_final.json')]

            if not range_files:
                print("❌ No completed range files found")
                return ""

            print(f"📁 Found {len(range_files)} range files: {range_files}")

            all_questions = []
            all_failed_pages = []
            total_duration = timedelta()
            range_metadata = []

            for range_file in sorted(range_files):
                file_path = os.path.join(output_dir, range_file)

                with open(file_path, 'r', encoding='utf-8') as f:
                    range_data = json.load(f)

                    # Collect questions
                    all_questions.extend(range_data.get('questions', []))

                    # Collect failed pages
                    all_failed_pages.extend(range_data.get('failed_pages', []))

                    # Collect metadata
                    metadata = range_data.get('metadata', {})
                    range_metadata.append({
                        'range_id': metadata.get('range_id'),
                        'pages': f"{metadata.get('start_page')}-{metadata.get('end_page')}",
                        'questions': metadata.get('total_questions', 0),
                        'answers': metadata.get('total_answers', 0),
                        'duration': metadata.get('duration', '0:00:00'),
                        'success_rate': metadata.get('success_rate', 0)
                    })

                    # Sum durations
                    if metadata.get('duration_seconds'):
                        total_duration += timedelta(seconds=metadata['duration_seconds'])

            # Create comprehensive output
            comprehensive_output = {
                'metadata': {
                    'scraper_version': 'distributed_comprehensive_v1.0',
                    'source': 'PropertyGuru HDB Questions - All 708 Pages (Distributed)',
                    'merge_timestamp': datetime.now().isoformat(),
                    'total_ranges': len(range_files),
                    'total_pages_attempted': 708,
                    'total_questions': len(all_questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                    'total_failed_pages': len(all_failed_pages),
                    'overall_success_rate': ((708 - len(all_failed_pages)) / 708) * 100,
                    'total_scraping_time': str(total_duration),
                    'avg_questions_per_page': len(all_questions) / 708 if len(all_questions) > 0 else 0,
                    'range_breakdown': range_metadata
                },
                'failed_pages': sorted(all_failed_pages),
                'questions': all_questions
            }

            # Save comprehensive file
            comprehensive_file = os.path.join(output_dir, f"propertyguru_distributed_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(comprehensive_file, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_output, f, indent=2, ensure_ascii=False)

            print(f"\n🎉 ALL RANGES MERGED SUCCESSFULLY!")
            print(f"📊 Comprehensive Statistics:")
            print(f"   • Total ranges merged: {len(range_files)}")
            print(f"   • Total questions: {len(all_questions):,}")
            print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
            print(f"   • Total failed pages: {len(all_failed_pages)}")
            print(f"   • Overall success rate: {((708 - len(all_failed_pages)) / 708) * 100:.1f}%")
            print(f"   • Total scraping time: {total_duration}")
            print(f"   • Comprehensive file: {comprehensive_file}")

            return comprehensive_file

        except Exception as e:
            print(f"💥 Error merging ranges: {e}")
            return ""

    def get_stats(self) -> Dict[str, Any]:
        """Get current scraping statistics for this range"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)

                stats = progress.get('stats', {})
                questions = progress.get('questions', [])

                return {
                    'range_id': self.range_id,
                    'pages_completed': stats.get('pages_completed', 0),
                    'total_pages_in_range': self.stats['total_pages'],
                    'total_questions': len(questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in questions),
                    'failed_pages_count': len(progress.get('failed_pages', [])),
                    'completion_percentage': stats.get('completion_percentage', 0),
                    'last_update': stats.get('last_update', 'Unknown')
                }
        except Exception as e:
            return {'error': f'Could not load stats for range {self.range_id}: {e}'}

        return {'error': f'No progress data found for range {self.range_id}'}

def parse_range(range_str: str) -> Tuple[int, int]:
    """Parse range string like '1-100' into start and end page numbers"""
    try:
        start, end = range_str.split('-')
        return int(start), int(end)
    except ValueError:
        raise argparse.ArgumentTypeError(f"Range must be in format 'start-end', got: {range_str}")

async def main():
    parser = argparse.ArgumentParser(description='Distributed PropertyGuru Scraper - Manual Terminal Distribution')
    parser.add_argument('--range', type=parse_range, required=True,
                       help='Page range to scrape (e.g., 1-100, 101-200)')
    parser.add_argument('--no-resume', action='store_true',
                       help='Start fresh instead of resuming from previous progress')
    parser.add_argument('--stats', action='store_true',
                       help='Show current scraping statistics for this range')
    parser.add_argument('--merge', action='store_true',
                       help='Merge all completed ranges into comprehensive file')

    args = parser.parse_args()

    if args.merge:
        comprehensive_file = DistributedPropertyGuruScraper.merge_all_ranges()
        if comprehensive_file:
            print(f"✅ Merge completed: {comprehensive_file}")
        return

    start_page, end_page = args.range

    # Validate range
    if start_page < 1 or end_page > 708 or start_page > end_page:
        print(f"❌ Invalid range: {start_page}-{end_page}")
        print("   Valid range: 1-708, start must be <= end")
        return

    scraper = DistributedPropertyGuruScraper(start_page, end_page)

    if args.stats:
        stats = scraper.get_stats()
        print(f"\n📊 Range {start_page}-{end_page} Statistics:")
        print("=" * 50)
        for key, value in stats.items():
            print(f"{key}: {value}")
        return

    # Main range scraping
    try:
        all_questions = await scraper.scrape_page_range(resume=not args.no_resume)

        print(f"\n✅ Range {start_page}-{end_page} completed!")
        print(f"📈 Questions collected: {len(all_questions):,}")
        print(f"📈 Answers collected: {sum(len(q.get('answers', [])) for q in all_questions):,}")
        print(f"📁 Range file: {scraper.final_file}")
        print(f"\n💡 To merge all ranges: python distributed_propertyguru_scraper.py --merge")

    except KeyboardInterrupt:
        print(f"\n⏸️  Range {start_page}-{end_page} interrupted by user")
        print("📁 Progress has been saved and can be resumed with:")
        print(f"   python distributed_propertyguru_scraper.py --range {start_page}-{end_page}")

    except Exception as e:
        print(f"\n💥 Critical error in range {start_page}-{end_page}: {e}")
        print("📁 Check progress files to see what was scraped")

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru DISTRIBUTED Scraper                         ║
║                                                                              ║
║  🎯 Target: All 708 pages divided into manual ranges                       ║
║  ⚡ Speed: Maximum parallelism with manual terminal control                 ║
║  ⏱️  Estimated time: 1-2 hours with multiple terminals                      ║
║                                                                              ║
║  🚀 DISTRIBUTED FEATURES:                                                    ║
║  ✅ Manual range assignment per terminal                                    ║
║  ✅ Independent progress tracking per range                                 ║
║  ✅ No conflicts between terminals                                          ║
║  ✅ Resume capability per range                                             ║
║  ✅ Automatic result merging                                                ║
║                                                                              ║
║  📋 USAGE EXAMPLES:                                                          ║
║  Terminal 1: python distributed_propertyguru_scraper.py --range 1-100      ║
║  Terminal 2: python distributed_propertyguru_scraper.py --range 101-200    ║
║  Terminal 3: python distributed_propertyguru_scraper.py --range 201-300    ║
║  Terminal 4: python distributed_propertyguru_scraper.py --range 301-400    ║
║  Terminal 5: python distributed_propertyguru_scraper.py --range 401-500    ║
║  Terminal 6: python distributed_propertyguru_scraper.py --range 501-600    ║
║  Terminal 7: python distributed_propertyguru_scraper.py --range 601-708    ║
║                                                                              ║
║  📊 MONITORING:                                                              ║
║  python distributed_propertyguru_scraper.py --range 1-100 --stats          ║
║  python distributed_propertyguru_scraper.py --merge                         ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
