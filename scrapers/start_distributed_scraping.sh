#!/bin/bash
"""
Quick Start Script for Distributed PropertyGuru Scraping
Automatically opens multiple terminals with different page ranges
"""

echo "🚀 Starting Distributed PropertyGuru Scraping"
echo "📊 Will open 7 terminals for maximum speed"
echo ""

# Activate virtual environment command
VENV_CMD="source venv/bin/activate"

# Base command
BASE_CMD="python distributed_propertyguru_scraper.py"

# Define ranges (you can adjust these)
RANGES=(
    "1-100"
    "101-200" 
    "201-300"
    "301-400"
    "401-500"
    "501-600"
    "601-708"
)

echo "📋 Terminal assignments:"
for i in "${!RANGES[@]}"; do
    terminal_num=$((i + 1))
    echo "   Terminal $terminal_num: Pages ${RANGES[$i]}"
done

echo ""
echo "🔄 Opening terminals..."

# Open terminals with different ranges
for i in "${!RANGES[@]}"; do
    range="${RANGES[$i]}"
    terminal_num=$((i + 1))
    
    # For macOS (using Terminal.app)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        osascript -e "
        tell application \"Terminal\"
            do script \"cd '$PWD' && $VENV_CMD && echo '🚀 Terminal $terminal_num: Scraping pages $range' && $BASE_CMD --range $range\"
        end tell
        "
    # For Linux (using gnome-terminal)
    elif command -v gnome-terminal &> /dev/null; then
        gnome-terminal -- bash -c "cd '$PWD' && $VENV_CMD && echo '🚀 Terminal $terminal_num: Scraping pages $range' && $BASE_CMD --range $range; exec bash"
    # For other systems, print commands
    else
        echo "Run this in Terminal $terminal_num:"
        echo "  cd '$PWD' && $VENV_CMD && $BASE_CMD --range $range"
        echo ""
    fi
    
    # Small delay between terminal openings
    sleep 1
done

echo ""
echo "✅ All terminals opened!"
echo ""
echo "📊 To monitor progress:"
echo "   python distributed_propertyguru_scraper.py --range 1-100 --stats"
echo "   python distributed_propertyguru_scraper.py --range 101-200 --stats"
echo "   (etc.)"
echo ""
echo "🔄 To merge results when done:"
echo "   python distributed_propertyguru_scraper.py --merge"
echo ""
echo "⏱️  Estimated completion time: 1-2 hours with all terminals running"
