#!/usr/bin/env python3
"""
Comprehensive PropertyGuru Scraper - All 708 Pages
Features:
- Resume capability from last successful page
- Streaming/batching with progress saves
- Enhanced error handling with retries
- Progress monitoring and statistics
- No testing limits - scrapes all questions per page
"""

import asyncio
import json
import os
import sys
import argparse
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from mvp_scraper.property_guru_scraper import PropertyGuruScraper

class ComprehensivePropertyGuruScraper:
    def __init__(self, output_dir: str = "data/processed"):
        self.output_dir = output_dir
        self.progress_file = os.path.join(output_dir, "comprehensive_progress.json")
        self.batch_size = 10  # Save progress every 10 pages
        self.max_retries = 3
        self.retry_delay = 5.0  # seconds
        self.page_delay = 3.0  # delay between pages

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Statistics tracking
        self.stats = {
            'start_time': None,
            'last_update': None,
            'total_questions': 0,
            'total_answers': 0,
            'pages_completed': 0,
            'pages_failed': [],
            'current_page': 0,
            'estimated_completion': None
        }

    async def scrape_single_page_with_retry(self, page_num: int) -> List[Dict[str, Any]]:
        """Scrape a single page with retry logic"""

        for attempt in range(self.max_retries):
            try:
                print(f"   📄 Page {page_num}, attempt {attempt + 1}/{self.max_retries}")

                # Create scraper instance
                scraper = PropertyGuruScraper()

                # Modify URL for specific page
                original_url = scraper.base_url
                scraper.base_url = f"https://www.propertyguru.com.sg/property-investment-questions/hdb-questions/{page_num}"

                try:
                    # Scrape with NO LIMITS - get all questions on the page
                    questions = await scraper.scrape_property_guru(max_questions=None)

                    if questions:
                        total_answers = sum(len(q.get('answers', [])) for q in questions)
                        print(f"   ✅ Success: {len(questions)} questions, {total_answers} answers")
                        return questions
                    else:
                        print(f"   ⚠️  No data found on attempt {attempt + 1}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                        else:
                            return []

                finally:
                    # Restore original URL
                    scraper.base_url = original_url

            except Exception as e:
                print(f"   ❌ Error on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    print(f"   💥 All retry attempts failed for page {page_num}")
                    return []

        return []

    def load_progress(self) -> Dict[str, Any]:
        """Load previous scraping progress"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)
                    print(f"📂 Loaded progress: page {progress.get('last_completed_page', 0)}, "
                          f"{len(progress.get('questions', []))} questions")
                    return progress
        except Exception as e:
            print(f"⚠️  Error loading progress: {e}")

        return {
            'last_completed_page': 0,
            'questions': [],
            'failed_pages': [],
            'stats': {}
        }

    def save_progress(self, current_page: int, all_questions: List[Dict[str, Any]],
                     failed_pages: List[int]):
        """Save current progress with streaming/batching"""
        try:
            # Update statistics
            self.stats.update({
                'last_update': datetime.now().isoformat(),
                'total_questions': len(all_questions),
                'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                'pages_completed': current_page,
                'current_page': current_page,
                'pages_failed': failed_pages
            })

            # Calculate estimated completion
            if current_page > 10:  # Only estimate after some progress
                elapsed = datetime.now() - datetime.fromisoformat(self.stats['start_time'])
                pages_per_second = current_page / elapsed.total_seconds()
                remaining_pages = 708 - current_page
                eta_seconds = remaining_pages / pages_per_second if pages_per_second > 0 else 0
                self.stats['estimated_completion'] = (datetime.now() + timedelta(seconds=eta_seconds)).isoformat()

            progress = {
                'last_completed_page': current_page,
                'questions': all_questions,
                'failed_pages': failed_pages,
                'stats': self.stats,
                'timestamp': datetime.now().isoformat()
            }

            # Save progress file
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress, f, indent=2, ensure_ascii=False)

            # Also save a backup every 50 pages
            if current_page % 50 == 0:
                backup_file = os.path.join(self.output_dir, f"backup_page_{current_page}.json")
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(progress, f, indent=2, ensure_ascii=False)
                print(f"   💾 Backup saved: {backup_file}")

        except Exception as e:
            print(f"⚠️  Error saving progress: {e}")

    def display_progress_stats(self, current_page: int, all_questions: List[Dict[str, Any]]):
        """Display current progress statistics"""
        if self.stats['start_time']:
            elapsed = datetime.now() - datetime.fromisoformat(self.stats['start_time'])
            completion_pct = (current_page / 708) * 100

            print(f"\n📊 Progress Statistics:")
            print(f"   • Pages: {current_page}/708 ({completion_pct:.1f}%)")
            print(f"   • Questions: {len(all_questions):,}")
            print(f"   • Answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
            print(f"   • Elapsed: {elapsed}")
            print(f"   • Avg time/page: {elapsed.total_seconds()/current_page:.1f}s")

            if self.stats.get('estimated_completion'):
                eta = datetime.fromisoformat(self.stats['estimated_completion'])
                print(f"   • ETA: {eta.strftime('%Y-%m-%d %H:%M:%S')}")
            print()

    async def scrape_all_pages(self, start_page: int = 1, resume: bool = True) -> List[Dict[str, Any]]:
        """Main comprehensive scraping function"""

        print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    COMPREHENSIVE PropertyGuru Scraper                       ║
║                                                                              ║
║  Features:                                                                   ║
║  ✅ Resume capability from last successful page                             ║
║  ✅ Streaming/batching with progress saves every 10 pages                   ║
║  ✅ Enhanced error handling with 3 retries per page                         ║
║  ✅ Progress monitoring with ETA calculations                               ║
║  ✅ No testing limits - scrapes ALL questions per page                      ║
║                                                                              ║
║  Target: All 708 pages (~14,000+ questions with answers)                   ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)

        # Load previous progress if resuming
        if resume:
            progress = self.load_progress()
            start_page = progress.get('last_completed_page', 0) + 1
            all_questions = progress.get('questions', [])
            failed_pages = progress.get('failed_pages', [])
            self.stats = progress.get('stats', self.stats)
        else:
            all_questions = []
            failed_pages = []

        # Set start time if not resuming
        if not self.stats.get('start_time'):
            self.stats['start_time'] = datetime.now().isoformat()

        print(f"🚀 Starting from page {start_page}")
        print(f"📈 Already have {len(all_questions)} questions")
        print(f"⏱️  Estimated total time: 12-24 hours")
        print()

        try:
            for page in range(start_page, 709):  # Pages 1 to 708
                print(f"🔄 Processing page {page}/708 ({(page/708)*100:.1f}% complete)")

                try:
                    questions = await self.scrape_single_page_with_retry(page)

                    if questions:
                        all_questions.extend(questions)
                        print(f"   📈 Total so far: {len(all_questions)} questions, "
                              f"{sum(len(q.get('answers', [])) for q in all_questions)} answers")
                    else:
                        failed_pages.append(page)
                        print(f"   ❌ Page {page} failed after all retries")

                    # Save progress every batch_size pages
                    if page % self.batch_size == 0:
                        self.save_progress(page, all_questions, failed_pages)
                        self.display_progress_stats(page, all_questions)

                    # Polite delay between pages
                    await asyncio.sleep(self.page_delay)

                except KeyboardInterrupt:
                    print(f"\n⏸️  Interrupted at page {page}")
                    self.save_progress(page - 1, all_questions, failed_pages)
                    raise

                except Exception as e:
                    print(f"   💥 Critical error on page {page}: {e}")
                    failed_pages.append(page)
                    continue

            # Final save
            self.save_progress(708, all_questions, failed_pages)

        except KeyboardInterrupt:
            print("\n⏸️  Scraping interrupted by user")
            print("📁 Progress has been saved and can be resumed later")
            return all_questions

        # Save final comprehensive output
        await self.save_final_output(all_questions, failed_pages)

        return all_questions

    async def save_final_output(self, all_questions: List[Dict[str, Any]], failed_pages: List[int]):
        """Save final comprehensive output"""
        try:
            end_time = datetime.now()
            start_time = datetime.fromisoformat(self.stats['start_time'])
            duration = end_time - start_time

            final_output = {
                'metadata': {
                    'scraper_version': 'comprehensive_v2.0',
                    'source': 'PropertyGuru HDB Questions - All 708 Pages',
                    'scraping_start': self.stats['start_time'],
                    'scraping_end': end_time.isoformat(),
                    'duration': str(duration),
                    'duration_seconds': duration.total_seconds(),
                    'total_pages_attempted': 708,
                    'total_questions': len(all_questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
                    'failed_pages_count': len(failed_pages),
                    'success_rate': ((708 - len(failed_pages)) / 708) * 100,
                    'avg_questions_per_page': len(all_questions) / 708 if len(all_questions) > 0 else 0,
                    'avg_answers_per_question': sum(len(q.get('answers', [])) for q in all_questions) / len(all_questions) if len(all_questions) > 0 else 0
                },
                'failed_pages': failed_pages,
                'questions': all_questions
            }

            # Save final comprehensive file
            final_file = os.path.join(self.output_dir, f"propertyguru_comprehensive_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(final_file, 'w', encoding='utf-8') as f:
                json.dump(final_output, f, indent=2, ensure_ascii=False)

            print(f"\n🎉 COMPREHENSIVE SCRAPING COMPLETED!")
            print(f"📊 Final Statistics:")
            print(f"   • Total questions: {len(all_questions):,}")
            print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
            print(f"   • Duration: {duration}")
            print(f"   • Failed pages: {len(failed_pages)}")
            print(f"   • Success rate: {((708 - len(failed_pages)) / 708) * 100:.1f}%")
            print(f"   • Final output: {final_file}")

            if failed_pages:
                print(f"   • Failed pages: {failed_pages[:20]}{'...' if len(failed_pages) > 20 else ''}")

        except Exception as e:
            print(f"⚠️  Error saving final output: {e}")

    async def retry_failed_pages(self, failed_pages: List[int] = None) -> List[Dict[str, Any]]:
        """Retry scraping failed pages"""
        if failed_pages is None:
            progress = self.load_progress()
            failed_pages = progress.get('failed_pages', [])

        if not failed_pages:
            print("✅ No failed pages to retry")
            return []

        print(f"🔄 Retrying {len(failed_pages)} failed pages")

        recovered_questions = []
        still_failed = []

        for page in failed_pages:
            print(f"🔄 Retrying page {page}")

            try:
                questions = await self.scrape_single_page_with_retry(page)

                if questions:
                    recovered_questions.extend(questions)
                    print(f"   ✅ Recovered: {len(questions)} questions")
                else:
                    still_failed.append(page)
                    print(f"   ❌ Still failed: page {page}")

                await asyncio.sleep(self.page_delay)

            except Exception as e:
                print(f"   💥 Error retrying page {page}: {e}")
                still_failed.append(page)

        print(f"\n📊 Retry Results:")
        print(f"   • Questions recovered: {len(recovered_questions)}")
        print(f"   • Pages still failed: {len(still_failed)}")

        return recovered_questions

    def get_stats(self) -> Dict[str, Any]:
        """Get current scraping statistics"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)

                stats = progress.get('stats', {})
                questions = progress.get('questions', [])

                return {
                    'last_completed_page': progress.get('last_completed_page', 0),
                    'total_questions': len(questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in questions),
                    'failed_pages_count': len(progress.get('failed_pages', [])),
                    'completion_percentage': (progress.get('last_completed_page', 0) / 708) * 100,
                    'last_update': stats.get('last_update', 'Unknown'),
                    'estimated_completion': stats.get('estimated_completion', 'Unknown')
                }
        except Exception as e:
            return {'error': f'Could not load stats: {e}'}

        return {'error': 'No progress data found'}

async def main():
    parser = argparse.ArgumentParser(description='Comprehensive PropertyGuru Scraper - All 708 Pages')
    parser.add_argument('--no-resume', action='store_true',
                       help='Start fresh instead of resuming from previous progress')
    parser.add_argument('--retry-failed', action='store_true',
                       help='Only retry previously failed pages')
    parser.add_argument('--stats', action='store_true',
                       help='Show current scraping statistics')
    parser.add_argument('--start-page', type=int, default=1,
                       help='Start from specific page (default: 1)')

    args = parser.parse_args()

    scraper = ComprehensivePropertyGuruScraper()

    if args.stats:
        stats = scraper.get_stats()
        print("\n📊 PropertyGuru Comprehensive Scraping Statistics:")
        print("=" * 60)
        for key, value in stats.items():
            print(f"{key}: {value}")
        return

    if args.retry_failed:
        print("🔄 Starting retry of failed pages...")
        recovered_questions = await scraper.retry_failed_pages()
        print(f"✅ Recovery completed: {len(recovered_questions)} questions recovered")
        return

    # Main comprehensive scraping
    try:
        all_questions = await scraper.scrape_all_pages(
            start_page=args.start_page,
            resume=not args.no_resume
        )

        print(f"\n✅ Scraping session completed!")
        print(f"📈 Total questions collected: {len(all_questions):,}")
        print(f"📈 Total answers collected: {sum(len(q.get('answers', [])) for q in all_questions):,}")

    except KeyboardInterrupt:
        print("\n⏸️  Scraping interrupted by user")
        print("📁 Progress has been saved and can be resumed with:")
        print("   python comprehensive_propertyguru_scraper.py")

    except Exception as e:
        print(f"\n💥 Critical error: {e}")
        print("📁 Check progress files in data/processed/ to see what was scraped")

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru COMPREHENSIVE Scraper                       ║
║                                                                              ║
║  🎯 Target: All 708 pages (~14,000+ questions with answers)                ║
║  ⏱️  Estimated time: 12-24 hours depending on network                       ║
║                                                                              ║
║  🚀 ENHANCED FEATURES:                                                       ║
║  ✅ Resume capability - automatically resumes from last page               ║
║  ✅ Progress tracking - saves every 10 pages + backups every 50            ║
║  ✅ Error recovery - 3 retries per page with exponential backoff           ║
║  ✅ Real-time monitoring - ETA calculations and progress stats             ║
║  ✅ No limits - scrapes ALL questions per page (not just 5)                ║
║  ✅ Streaming/batching - efficient memory usage for large datasets         ║
║                                                                              ║
║  📋 USAGE:                                                                   ║
║  python comprehensive_propertyguru_scraper.py                               ║
║  python comprehensive_propertyguru_scraper.py --no-resume                   ║
║  python comprehensive_propertyguru_scraper.py --retry-failed                ║
║  python comprehensive_propertyguru_scraper.py --stats                       ║
║  python comprehensive_propertyguru_scraper.py --start-page 100              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
