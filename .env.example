# BTO Sentiment Scraper Configuration
# Copy this file to .env and fill in your actual values

# Gemini API Configuration (Optional)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Scraping Configuration (Optional - defaults are provided)
# SCRAPER_DELAY=2.0
# SCRAPER_TIMEOUT=30
# SCRAPER_RETRIES=3

# Output Configuration (Optional)
# OUTPUT_DIR=data/processed
# LOG_LEVEL=INFO

# PropertyGuru Comprehensive Scraping (Optional)
# PROPERTYGURU_TOTAL_PAGES=708
# PROPERTYGURU_BATCH_SIZE=50
# PROPERTYGURU_CONCURRENCY=5
