import asyncio
import json
import sys
print(sys.path)
from mvp_scraper.property_guru_scraper import scrape_property_guru
from mvp_scraper.reddit_scraper import scrape_reddit
from mvp_scraper.hardware_zone_scraper import scrape_hardware_zone

async def main():
    # --- PropertyGuru Demo ---
    print("--- Scraping PropertyGuru (DEMO) ---")
    property_guru_data = await scrape_property_guru(max_questions=5)
    with open("data/processed/propertyguru_mvp_demo.json", "w") as f:
        json.dump(property_guru_data, f, indent=2)
    print("PropertyGuru demo data saved.")

    # --- Reddit Demo ---
    print("\n--- Scraping Reddit (DEMO) ---")
    reddit_data = await scrape_reddit()
    with open("data/processed/reddit_mvp_demo.json", "w") as f:
        json.dump(reddit_data, f, indent=2)
    print("Reddit demo data saved.")

    # --- HardwareZone Demo ---
    print("\n--- Scraping HardwareZone (DEMO) ---")
    hwz_url = "https://forums.hardwarezone.com.sg/threads/bto-inflation-quite-hiong.7092360/"
    hardware_zone_data = await scrape_hardware_zone(url=hwz_url)
    with open("data/processed/hardwarezone_mvp_demo.json", "w") as f:
        json.dump(hardware_zone_data, f, indent=2)
    print("HardwareZone demo data saved.")

if __name__ == "__main__":
    asyncio.run(main())