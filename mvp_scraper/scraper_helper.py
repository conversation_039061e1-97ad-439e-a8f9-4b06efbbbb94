import asyncio
import time
from typing import Dict, List, Any, Optional
import logging
import requests
from bs4 import BeautifulSoup

try:
    from crawl4ai.async_webcrawler import AsyncWebCrawler
    from crawl4ai.async_configs import CrawlerRunConfig
    CRAWL4AI_AVAILABLE = True
except ImportError:
    print("crawl4ai not available, using requests + BeautifulSoup fallback")
    CRAWL4AI_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
except ImportError:
    print("Selenium not available")
    SELENIUM_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ScraperHelper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    async def scrape_with_helper(self, url: str, selectors: Dict[str, str],
                                options: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Main helper function that tries crawl4ai first, falls back to Selenium

        Args:
            url: Target URL to scrape
            selectors: Dictionary of CSS selectors for different elements
            options: Additional options for scraping

        Returns:
            List of scraped data dictionaries
        """
        if options is None:
            options = {}

        crawl4ai_result_data = []
        try:
            # Attempt crawl4ai first
            logger.info(f"Attempting crawl4ai for {url}")
            crawl4ai_result = await self._scrape_with_crawl4ai(url, selectors, options)
            processed_result = self._process_crawl4ai_result(crawl4ai_result)

            # Check if the result is substantial
            if processed_result:
                # If it's just a small HTML string, it might be a failure page
                if 'html' in processed_result[0] and len(processed_result[0]['html']) < 500:
                    logger.warning(f"crawl4ai returned very small HTML ({len(processed_result[0]['html'])} bytes). Assuming failure.")
                else:
                    crawl4ai_result_data = processed_result

        except Exception as error:
            logger.warning(f"crawl4ai failed for {url}: {error}")
            # Proceed to Selenium fallback

        # If crawl4ai didn't produce good results, fall back to Selenium
        if not crawl4ai_result_data:
            logger.info(f"crawl4ai did not yield results for {url}. Falling back to Selenium.")
            try:
                selenium_result = await self._scrape_with_selenium(url, selectors, options)
                return self._process_selenium_result(selenium_result)
            except Exception as selenium_error:
                logger.error(f"Selenium also failed for {url}: {selenium_error}")
                return []

        return crawl4ai_result_data

    async def _scrape_with_crawl4ai(self, url: str, selectors: Dict[str, str],
                                   options: Optional[Dict[str, Any]]) -> Any:
        """Scrape using crawl4ai"""
        try:
            # Ensure options is a dict
            if options is None:
                options = {}

            # Handle wait_for properly - it should be a string or None
            wait_for = options.get("wait_for")
            if isinstance(wait_for, int):
                wait_for = str(wait_for)
            elif wait_for is None:
                wait_for = ""

            # Optimized CrawlerRunConfig for faster scraping
            config = CrawlerRunConfig(
                wait_for=wait_for,
                screenshot=options.get("screenshot", False),
                css_selector=selectors.get("container", ""),
                verbose=False  # Reduced verbosity for speed
            )
            async with self.crawl4ai_scraper as crawler:
                result = await crawler.arun(url=url, config=config)
            return result

        except Exception as e:
            logger.error(f"crawl4ai error: {e}")
            raise

    async def scrape_with_selenium(self, url: str, selectors: Dict[str, str],
                                   options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Scrape using Selenium directly (no crawl4ai fallback)"""
        try:
            logger.info(f"Using Selenium for {url}")
            selenium_result = await self._scrape_with_selenium(url, selectors, options)
            return self._process_selenium_result(selenium_result)
        except Exception as selenium_error:
            logger.error(f"Selenium failed for {url}: {selenium_error}")
            return []

    async def _scrape_with_selenium(self, url: str, selectors: Dict[str, str],
                                   options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Scrape using Selenium as fallback"""
        driver = None
        try:
            # Configure Chrome options
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

            driver = webdriver.Chrome(options=chrome_options)
            driver.get(url)

            # Wait for page to load
            wait_time = options.get("wait_time", 5)
            await asyncio.sleep(wait_time)

            # Extract data based on selectors
            scraped_data = []

            # Handle different types of selectors
            if "container" in selectors:
                containers = driver.find_elements(By.CSS_SELECTOR, selectors["container"])

                for container in containers:
                    item_data = {}

                    # Extract text content
                    if "text" in selectors:
                        try:
                            text_elements = container.find_elements(By.CSS_SELECTOR, selectors["text"])
                            item_data["text"] = " ".join([elem.text for elem in text_elements])
                        except:
                            item_data["text"] = ""

                    # Extract author
                    if "author" in selectors:
                        try:
                            author_elements = container.find_elements(By.CSS_SELECTOR, selectors["author"])
                            item_data["author"] = author_elements[0].text if author_elements else ""
                        except:
                            item_data["author"] = ""

                    # Extract date
                    if "date" in selectors:
                        try:
                            date_elements = container.find_elements(By.CSS_SELECTOR, selectors["date"])
                            item_data["date"] = date_elements[0].text if date_elements else ""
                        except:
                            item_data["date"] = ""

                    # Extract URL if available
                    if "url" in selectors:
                        try:
                            url_elements = container.find_elements(By.CSS_SELECTOR, selectors["url"])
                            if url_elements:
                                url = url_elements[0].get_attribute("href")
                                # Handle relative URLs
                                if url and url.startswith('/'):
                                    base_url = url.split('/')[0] + '//' + url.split('/')[2]
                                    url = base_url + url
                                item_data["url"] = url if url else ""
                            else:
                                item_data["url"] = ""
                        except:
                            item_data["url"] = ""

                    if item_data.get("text"):  # Only add if we have some text content
                        scraped_data.append(item_data)

            return scraped_data

        except Exception as e:
            logger.error(f"Selenium error: {e}")
            raise
        finally:
            if driver:
                driver.quit()

    def _process_crawl4ai_result(self, result: Any) -> List[Dict[str, Any]]:
        """Process crawl4ai result into standardized format"""
        processed_data = []

        try:
            # Debug: Print the result structure
            logger.info(f"crawl4ai result type: {type(result)}")

            # Extract data from crawl4ai result
            if hasattr(result, 'extracted_content') and result.extracted_content:
                logger.info(f"Found extracted_content with {len(result.extracted_content)} items")
                for item in result.extracted_content:
                    processed_item = {
                        "text": item.get("text", ""),
                        "author": item.get("author", ""),
                        "date": item.get("date", ""),
                        "url": item.get("url", ""),
                        "source": "crawl4ai"
                    }
                    processed_data.append(processed_item)
            elif hasattr(result, 'html') and result.html:
                logger.info(f"Found HTML content, length: {len(result.html)}")
                # The individual scrapers will be responsible for parsing the HTML
                processed_data.append({'html': result.html, 'source': 'crawl4ai'})
            elif hasattr(result, 'content'):
                logger.info(f"Found content attribute")
                processed_data.append({'html': str(result.content), 'source': 'crawl4ai'})
            else:
                logger.warning(f"No known attributes found in crawl4ai result")
                # Try to convert result to string and use as text
                processed_item = {
                    "text": str(result) if result else "",
                    "author": "",
                    "date": "",
                    "url": "",
                    "source": "crawl4ai"
                }
                processed_data.append(processed_item)

        except Exception as e:
            logger.error(f"Error processing crawl4ai result: {e}")

        return processed_data

    def _process_selenium_result(self, result: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process Selenium result into standardized format"""
        processed_data = []

        for item in result:
            processed_item = {
                "text": item.get("text", ""),
                "author": item.get("author", ""),
                "date": item.get("date", ""),
                "url": item.get("url", ""),
                "source": "selenium"
            }
            processed_data.append(processed_item)

        return processed_data

    async def scrape_with_delay(self, url: str, selectors: Dict[str, str],
                               options: Optional[Dict[str, Any]] = None, delay: float = 2.0) -> List[Dict[str, Any]]:
        """Scrape with a delay to be polite to servers"""
        await asyncio.sleep(delay)
        return await self.scrape_with_helper(url, selectors, options)

    async def get_html_with_selenium(self, url: str) -> str:
        """Get the full HTML content of a page using Selenium (headless Chrome)."""
        import asyncio
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        import time

        def _get_html():
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')
            driver = webdriver.Chrome(options=chrome_options)
            driver.get(url)
            time.sleep(3)
            html = driver.page_source
            driver.quit()
            return html

        loop = asyncio.get_event_loop()
        html = await loop.run_in_executor(None, _get_html)
        return html