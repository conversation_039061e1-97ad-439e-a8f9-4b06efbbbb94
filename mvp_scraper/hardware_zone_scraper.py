import asyncio
from typing import List, Dict, Any, Optional
from .scraper_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>per
import logging
from bs4 import BeautifulSoup
from urllib.parse import urljoin

logger = logging.getLogger(__name__)

class HardwareZoneScraper:
    def __init__(self):
        self.helper = ScraperHelper()
        self.base_url = "https://forums.hardwarezone.com.sg"

    async def scrape_single_thread(self, thread_url: str) -> List[Dict[str, Any]]:
        """
        Scrapes a single HardwareZone thread for the main post and all comments,
        handling pagination to scrape all pages.

        Args:
            thread_url: The URL of the thread to scrape.

        Returns:
            A list containing a single dictionary with the thread data,
            including a nested list of all comments from all pages.
        """
        logger.info(f"Starting single thread scraping for: {thread_url}")
        try:
            # Scrape all pages of the thread
            all_thread_content = await self._scrape_all_pages(thread_url)

            if not all_thread_content or not all_thread_content.get("posts"):
                logger.warning(f"No content found for thread: {thread_url}")
                return []

            # Process the aggregated data from all pages
            return self._process_thread_data(all_thread_content, thread_url)
        except Exception as e:
            logger.error(f"Error scraping single thread {thread_url}: {e}", exc_info=True)
            return []

    async def _scrape_all_pages(self, start_url: str) -> Optional[Dict[str, Any]]:
        """Scrapes all pages of a thread, following 'Next' links."""
        all_posts = []
        title = "No Title Found"
        current_url = start_url
        is_first_page = True

        while current_url:
            logger.info(f"Scraping page: {current_url}")
            html_content = await self.helper.get_html_with_selenium(current_url)
            if not html_content:
                logger.error(f"Failed to get HTML from {current_url}")
                break

            parsed_page = self._parse_thread_html_content(html_content, is_first_page)
            if not parsed_page:
                logger.warning(f"Could not parse content from {current_url}")
                break

            if is_first_page:
                title = parsed_page.get("title", title)
                is_first_page = False

            all_posts.extend(parsed_page.get("posts", []))
            
            next_page_path = parsed_page.get("next_page_url")
            if next_page_path:
                current_url = urljoin(self.base_url, next_page_path)
                await asyncio.sleep(1)  # Politeness delay
            else:
                current_url = None
                logger.info("No more pages to scrape.")

        if not all_posts:
            return None

        return {"title": title, "posts": all_posts}

    def _parse_thread_html_content(self, html_content: str, is_first_page: bool) -> Optional[Dict[str, Any]]:
        """Parse HTML content of a single thread page."""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        parsed_data = {}

        if is_first_page:
            title_elem = soup.select_one("title")
            title = title_elem.get_text(strip=True) if title_elem else "No Title Found"
            if "|" in title:
                title = title.split("|")[0].strip()
            if "HardwareZone.com.sg" in title:
                title = title.replace(" - HardwareZone.com.sg", "").strip()
            parsed_data["title"] = title

        posts_data = []
        post_containers = soup.select("article.message")

        for i, container in enumerate(post_containers):
            # Skip the first post if it's not the first page, as it's a duplicate from the previous page's quote
            if not is_first_page and i == 0:
                op_post_check = container.select_one(".message-attribution-main")
                if op_post_check: # This is likely a quoted OP, skip it
                    continue

            text_elem = container.select_one(".bbWrapper")
            author_elem = container.select_one(".message-name .username")
            date_elem = container.select_one("time[datetime]")

            if not text_elem or not author_elem:
                continue

            post_data = {
                "text": text_elem.get_text(strip=True),
                "author": author_elem.get_text(strip=True),
                "date": date_elem["datetime"] if date_elem and date_elem.has_attr("datetime") else "",
                "is_op": is_first_page and i == 0
            }
            posts_data.append(post_data)
        
        parsed_data["posts"] = posts_data

        # Find next page link
        next_page_elem = soup.select_one("a.pageNav-jump--next")
        if next_page_elem and next_page_elem.has_attr("href"):
            parsed_data["next_page_url"] = next_page_elem["href"]
        else:
            parsed_data["next_page_url"] = None

        return parsed_data

    def _process_thread_data(self, thread_content: Dict[str, Any], thread_url: str) -> List[Dict[str, Any]]:
        """Processes raw thread data from all pages into a structured format."""
        posts = thread_content["posts"]
        if not posts:
            return []

        op_post_raw = posts[0]
        main_post = {
            "source": "hardwarezone",
            "type": "post",
            "title": thread_content["title"],
            "text": op_post_raw.get("text", ""),
            "author": op_post_raw.get("author", "Unknown"),
            "date": op_post_raw.get("date", ""),
            "url": thread_url,
            "comments": [],
            "comment_count_actual": 0,
            "raw_data": op_post_raw,
        }

        comments = []
        for comment_raw in posts[1:]:
            comment = {
                "text": comment_raw.get("text", ""),
                "author": comment_raw.get("author", "Unknown"),
                "date": comment_raw.get("date", ""),
                "post_url": thread_url,
            }
            comments.append(comment)

        main_post["comments"] = comments
        main_post["comment_count_actual"] = len(comments)

        return [main_post]

async def scrape_hardware_zone(url: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Convenience function to scrape HardwareZone.

    If a URL is provided, it scrapes that single thread, including all pages.
    """
    scraper = HardwareZoneScraper()
    if url:
        return await scraper.scrape_single_thread(url)
    else:
        logger.warning("No URL provided. Full search scraping not implemented in this flow.")
        return []
