import asyncio
from typing import List, Dict, Any
from .scraper_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import logging
from bs4 import BeautifulSoup
import re
import json

logger = logging.getLogger(__name__)

class RedditScraper:
    def __init__(self):
        self.helper = ScraperHelper()
        self.search_url = "https://www.reddit.com/search/?q=BTO+singapore&cId=bfd9472d-3b5d-4804-a85a-ae6eb6e758cd&iId=2dd6ea0d-b7f7-4b34-96f3-c32c584df21e"

    async def scrape_reddit(self) -> List[Dict[str, Any]]:
        """
        Scrape Reddit for posts about BTO

        Returns:
            List of dictionaries containing scraped Reddit data
        """
        logger.info("Starting Reddit scraping")

        all_data = []

        try:
            # Scrape search results to get post URLs
            html_content_search = await self.helper.get_html_with_selenium(self.search_url)
            search_data = self._parse_search_results_html(html_content_search)

            # Then scrape individual posts (limit to first 3 to avoid rate limiting)
            for i, post_data in enumerate(search_data[:3]):
                post_url = post_data.get('url', '')
                if post_url:
                    logger.info(f"Scraping post {i+1}: {post_url}")
                    html_content_post = await self.helper.get_html_with_selenium(post_url)
                    post_content = self._parse_post_content_html(html_content_post)

                    # Add post URL to comments for reference
                    for item in post_content:
                        item['post_url'] = post_url

                    all_data.extend(post_content)

                    # Add delay between posts
                    await asyncio.sleep(2)

            # Process the data for Reddit specific format
            processed_data = self._process_reddit_data(all_data)

            logger.info(f"Successfully scraped {len(processed_data)} items from Reddit")
            return processed_data

        except Exception as e:
            logger.error(f"Error scraping Reddit: {e}")
            return []

    def _parse_search_results_html(self, html_content: str) -> List[Dict[str, Any]]:
        """Parse HTML content of search results page"""
        processed_data = []
        soup = BeautifulSoup(html_content, 'html.parser')

        # Try multiple selectors for Reddit posts
        post_containers = soup.select('div[data-testid="search-sdui-post"]')
        if not post_containers:
            post_containers = soup.select('div[data-testid="search-post-unit"]')
        if not post_containers:
            post_containers = soup.select('shreddit-post')

        logger.info(f"Found {len(post_containers)} post containers")

        for container in post_containers:
            item_data = {}

            # Try multiple selectors for title/text
            title_elem = container.select_one('a[data-testid="post-title-text"]')
            if not title_elem:
                title_elem = container.select_one('h3 a')
            if not title_elem:
                title_elem = container.select_one('[data-testid="post-title"] a')

            if title_elem:
                item_data['text'] = title_elem.get_text(strip=True)
                href = title_elem.get('href', '')
                if href.startswith('/'):
                    item_data['url'] = "https://www.reddit.com" + href
                else:
                    item_data['url'] = href

            # Try multiple selectors for date
            date_elem = container.select_one('faceplate-timeago')
            if not date_elem:
                date_elem = container.select_one('[datetime]')
            if date_elem:
                item_data['date'] = date_elem.get('ts', '') or date_elem.get('datetime', '')

            # Try to get author
            author_elem = container.select_one('a[data-testid="post_author_link"]')
            if not author_elem:
                author_elem = container.select_one('[href*="/user/"]')
            if author_elem:
                item_data['author'] = author_elem.get_text(strip=True).replace('u/', '')

            # Try to get subreddit
            subreddit_elem = container.select_one('a[data-testid="subreddit-link"]')
            if not subreddit_elem:
                subreddit_elem = container.select_one('[href*="/r/"]')
            if subreddit_elem:
                item_data['subreddit'] = subreddit_elem.get_text(strip=True).replace('r/', '')

            # Try to get upvotes
            upvote_elem = container.select_one('[data-testid="vote-arrows"] span')
            if upvote_elem:
                item_data['upvotes'] = upvote_elem.get_text(strip=True)

            if item_data.get('text') and item_data.get('url'):
                processed_data.append(item_data)

        return processed_data

    def _parse_post_content_html(self, html_content: str) -> List[Dict[str, Any]]:
        """Parse HTML content of individual post page"""
        processed_data = []
        soup = BeautifulSoup(html_content, 'html.parser')

        # First, try to get the main post content
        post_data = self._extract_main_post(soup)
        if post_data:
            processed_data.append(post_data)

        # Find all comment containers - try multiple selectors
        comment_containers = soup.select('shreddit-comment')
        if not comment_containers:
            comment_containers = soup.select('[data-testid="comment"]')
        if not comment_containers:
            comment_containers = soup.select('.Comment')

        logger.info(f"Found {len(comment_containers)} comment containers")

        for container in comment_containers:
            item_data = {}

            # Extract comment text - try multiple selectors
            text_elem = container.select_one('div[slot="comment"] div.md p')
            if not text_elem:
                text_elem = container.select_one('[slot="comment"]')
            if not text_elem:
                text_elem = container.select_one('[data-testid="comment-text"]')
            if not text_elem:
                text_elem = container.select_one('.RichTextJSON-root')

            if text_elem:
                item_data['text'] = text_elem.get_text(strip=True)

            # Extract author - try multiple selectors
            author_elem = container.select_one('div.author-name-meta a')
            if not author_elem:
                author_elem = container.select_one('[slot="authorName"]')
            if not author_elem:
                author_elem = container.select_one('[data-testid="comment-author-link"]')
            if not author_elem:
                author_elem = container.select_one('a[href*="/user/"]')

            if author_elem:
                item_data['author'] = author_elem.get_text(strip=True).replace('u/', '')

            # Extract date - try multiple selectors
            date_elem = container.select_one('faceplate-timeago')
            if not date_elem:
                date_elem = container.select_one('[datetime]')
            if date_elem:
                item_data['date'] = date_elem.get('ts', '') or date_elem.get('datetime', '')

            # Try to get upvotes
            upvote_elem = container.select_one('[data-testid="comment-vote-arrows"] span')
            if not upvote_elem:
                upvote_elem = container.select_one('.vote span')
            if upvote_elem:
                item_data['upvotes'] = upvote_elem.get_text(strip=True)

            # Mark as comment type
            item_data['type'] = 'comment'

            if item_data.get('text'):
                processed_data.append(item_data)

        return processed_data

    def _extract_main_post(self, soup) -> Dict[str, Any]:
        """Extract main post content from Reddit post page"""
        post_data = {}

        # Try to find the main post
        post_elem = soup.select_one('shreddit-post')
        if not post_elem:
            post_elem = soup.select_one('[data-testid="post-content"]')

        if post_elem:
            # Get post title
            title_elem = post_elem.select_one('[data-testid="post-title"]')
            if not title_elem:
                title_elem = post_elem.select_one('h1')
            if title_elem:
                post_data['title'] = title_elem.get_text(strip=True)
                post_data['text'] = title_elem.get_text(strip=True)

            # Get post content/body
            content_elem = post_elem.select_one('[data-testid="post-text-container"]')
            if not content_elem:
                content_elem = post_elem.select_one('[slot="text-body"]')
            if content_elem:
                content_text = content_elem.get_text(strip=True)
                if content_text:
                    post_data['text'] = content_text

            # Get author
            author_elem = post_elem.select_one('[data-testid="post-author-link"]')
            if not author_elem:
                author_elem = post_elem.select_one('a[href*="/user/"]')
            if author_elem:
                post_data['author'] = author_elem.get_text(strip=True).replace('u/', '')

            # Get subreddit
            subreddit_elem = post_elem.select_one('[data-testid="subreddit-link"]')
            if not subreddit_elem:
                subreddit_elem = post_elem.select_one('a[href*="/r/"]')
            if subreddit_elem:
                post_data['subreddit'] = subreddit_elem.get_text(strip=True).replace('r/', '')

            # Get date
            date_elem = post_elem.select_one('faceplate-timeago')
            if not date_elem:
                date_elem = post_elem.select_one('[datetime]')
            if date_elem:
                post_data['date'] = date_elem.get('ts', '') or date_elem.get('datetime', '')

            # Get upvotes
            upvote_elem = post_elem.select_one('[data-testid="vote-arrows"] span')
            if upvote_elem:
                post_data['upvotes'] = upvote_elem.get_text(strip=True)

            # Mark as post type
            post_data['type'] = 'post'

        return post_data if post_data.get('text') else {}

    def _process_reddit_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process Reddit data to match PropertyGuru success pattern

        Args:
            raw_data: Raw scraped data

        Returns:
            Processed data with Reddit specific structure matching PropertyGuru pattern
        """
        processed_data = []
        current_post = None
        comments = []

        for item in raw_data:
            text = item.get('text', '')
            if not text.strip():
                continue

            item_type = item.get('type', 'comment')

            if item_type == 'post':
                # If we have a previous post with comments, save it
                if current_post:
                    current_post['comments'] = comments
                    current_post['comment_count_actual'] = len(comments)
                    processed_data.append(current_post)

                # Start new post
                current_post = {
                    'source': 'reddit',
                    'type': 'post',
                    'text': text,
                    'author': item.get('author', 'Unknown'),
                    'date': item.get('date', ''),
                    'url': item.get('url', ''),
                    'title': item.get('title', text[:100] + '...' if len(text) > 100 else text),
                    'subreddit': item.get('subreddit', ''),
                    'upvotes': item.get('upvotes', ''),
                    'comment_count': '0',  # Will be updated
                    'raw_data': {
                        'text': text,
                        'author': item.get('author', ''),
                        'date': item.get('date', ''),
                        'subreddit': item.get('subreddit', ''),
                        'upvotes': item.get('upvotes', ''),
                        'url': item.get('url', ''),
                        'source': 'selenium'
                    },
                    'comments': [],
                    'comment_count_actual': 0
                }
                comments = []

            else:  # comment
                comment_data = {
                    'comment_id': f"c{len(comments) + 1}",
                    'text': text,
                    'author': item.get('author', 'Unknown'),
                    'date': item.get('date', ''),
                    'upvotes': item.get('upvotes', ''),
                    'post_url': item.get('post_url', current_post.get('url', '') if current_post else '')
                }
                comments.append(comment_data)

        # Don't forget the last post
        if current_post:
            current_post['comments'] = comments
            current_post['comment_count_actual'] = len(comments)
            current_post['comment_count'] = f"{len(comments)} Comments"
            processed_data.append(current_post)

        # If we only have comments without posts, create individual comment items
        if not processed_data and comments:
            for comment in comments:
                processed_item = {
                    'source': 'reddit',
                    'type': 'comment',
                    'text': comment['text'],
                    'author': comment['author'],
                    'date': comment['date'],
                    'url': comment.get('post_url', ''),
                    'raw_data': comment
                }
                processed_data.append(processed_item)

        return processed_data

async def scrape_reddit() -> List[Dict[str, Any]]:
    """Convenience function to scrape Reddit"""
    scraper = RedditScraper()
    return await scraper.scrape_reddit()