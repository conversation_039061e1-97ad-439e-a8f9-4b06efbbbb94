import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from .scraper_helper import <PERSON>rap<PERSON><PERSON><PERSON>per
import logging
from bs4 import BeautifulSoup
import aiofiles

logger = logging.getLogger(__name__)

class PropertyGuruScraper:
    def __init__(self, output_dir: str = "data/processed"):
        self.helper = ScraperHelper()
        self.base_url = "https://www.propertyguru.com.sg/property-investment-questions/hdb-questions"
        self.answer_concurrency = 5  # Reduced for stability
        self.output_dir = output_dir
        self.progress_file = os.path.join(output_dir, "propertyguru_progress.json")
        self.batch_size = 50  # Save progress every 50 questions
        self.max_retries = 3
        self.retry_delay = 5.0  # seconds

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
    async def scrape_property_guru(self, max_questions: int = 5) -> List[Dict[str, Any]]:
        """
        Scrape PropertyGuru HDB questions and answers
        
        Args:
            max_questions: Maximum number of questions to process (for testing)
            
        Returns:
            List of dictionaries containing scraped Q&A data
        """
        logger.info("Starting PropertyGuru scraping")
        
        # Updated selectors for PropertyGuru - use the working selector
        selectors = {
            "container": "div.question-div",  # Each question div contains all the data
            "text": "div.question-content article",  # Question text
            "author": "div.question-poster-info span.question-screen-name",  # Author name
            "date": "div.question-poster-info__sub-info",  # Date
            "category": "div.question-poster-info a.question-category",  # Category
            "answer_count": "div.question-info .answer-count",  # Answer count
            "url": "div.question-content a"  # Question URL
        }
        
        # Optimized options for faster scraping
        options = {
            "wait_for": 2000,  # Reduced from 5000 to 2000ms
            "wait_time": 5,  # Reduced from 10 to 5 seconds
            "screenshot": False
        }
        
        try:
            # Scrape the main page
            scraped_data = await self.helper.scrape_with_delay(
                self.base_url, 
                selectors, 
                options, 
                delay=1.0  # Reduced from 2.0 to 1.0
            )

            if scraped_data and 'html' in scraped_data[0]:
                scraped_data = self._parse_html_content(scraped_data[0]['html'])
            
            logger.info(f"Raw scraped data length: {len(scraped_data) if scraped_data else 0}")
            
            # Process the data for PropertyGuru specific format
            processed_data = self._process_property_guru_data(scraped_data)
            
            # Limit the number of questions for testing
            if max_questions and len(processed_data) > max_questions:
                processed_data = processed_data[:max_questions]
                logger.info(f"Limited to {max_questions} questions for testing")
            
            # Now scrape answers for each question in parallel
            enhanced_data = await self._scrape_answers_for_questions(processed_data)
            
            logger.info(f"Successfully scraped {len(enhanced_data)} items from PropertyGuru with answers")
            return enhanced_data
            
        except Exception as e:
            logger.error(f"Error scraping PropertyGuru: {e}")
            return []
    
    def _parse_html_content(self, html_content: str) -> List[Dict[str, Any]]:
        """Parse HTML content to extract structured data"""
        from bs4 import BeautifulSoup
        
        processed_data = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find all question divs
            question_divs = soup.find_all('div', class_='question-div')
            logger.info(f"Found {len(question_divs)} question divs")
            
            for div in question_divs:
                item_data = {}
                
                # Extract question text
                question_content = div.find('div', class_='question-content')
                if question_content:
                    article = question_content.find('article')
                    if article:
                        item_data['text'] = article.get_text(strip=True)
                
                # Extract author
                poster_info = div.find('div', class_='question-poster-info')
                if poster_info:
                    author_span = poster_info.find('span', class_='question-screen-name')
                    if author_span:
                        item_data['author'] = author_span.get_text(strip=True)
                
                # Extract date
                if poster_info:
                    date_div = poster_info.find('div', class_='question-poster-info__sub-info')
                    if date_div:
                        item_data['date'] = date_div.get_text(strip=True)
                
                # Extract category
                if poster_info:
                    category_link = poster_info.find('a', class_='question-category')
                    if category_link:
                        item_data['category'] = category_link.get_text(strip=True)
                
                # Extract answer count
                question_info = div.find('div', class_='question-info')
                if question_info:
                    answer_count = question_info.find('span', class_='answer-count')
                    if answer_count:
                        item_data['answer_count'] = answer_count.get_text(strip=True)
                
                # Extract URL
                question_content = div.find('div', class_='question-content')
                if question_content:
                    link = question_content.find('a')
                    if link and link.get('href'):
                        item_data['url'] = 'https://www.propertyguru.com.sg' + link['href']
                
                if item_data.get('text'):  # Only add if we have question text
                    item_data['source'] = 'crawl4ai'
                    processed_data.append(item_data)
            
        except Exception as e:
            logger.error(f"Error parsing HTML content: {e}")
        
        return processed_data

    async def _scrape_answers_for_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Scrape answers for each question by following the question URLs, in parallel (max 10 concurrent)
        
        Args:
            questions: List of questions with URLs
            
        Returns:
            Enhanced questions with answers included
        """
        semaphore = asyncio.Semaphore(self.answer_concurrency)
        enhanced_questions = [None] * len(questions)

        async def process_question(i, question):
            logger.info(f"Scraping answers for question {i+1}/{len(questions)}")
            question_url = question.get('url', '')
            if not question_url:
                logger.warning(f"No URL found for question {i+1}, skipping")
                question['answers'] = []
                question['answer_count_actual'] = 0
                enhanced_questions[i] = question
                return
            try:
                async with semaphore:
                    # Always use Selenium for answer pages
                    try:
                        answers = await self._scrape_answers_for_question(question_url)
                        question['answers'] = answers
                        question['answer_count_actual'] = len(answers)
                        print(f"Found {len(answers)} answers for question")
                    except Exception as e:
                        print(f"Error scraping answers for question: {e}")
                        question['answers'] = []
                        question['answer_count_actual'] = 0
                enhanced_questions[i] = question
            except Exception as e:
                logger.error(f"Error scraping answers for question {i+1}: {e}")
                question['answers'] = []
                question['answer_count_actual'] = 0
                enhanced_questions[i] = question

        await asyncio.gather(*(process_question(i, q) for i, q in enumerate(questions)))
        return enhanced_questions
    
    async def _scrape_answers_for_question(self, question_url: str) -> List[Dict]:
        """Scrape answers for a specific question URL using Selenium."""
        try:
            # Always use Selenium for answer pages as they require JavaScript
            html_content = await self.helper.get_html_with_selenium(question_url)
            
            if not html_content:
                print(f"Failed to get HTML content for answer page: {question_url}")
                return []
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find all answer containers
            answer_containers = soup.find_all('div', class_='askguru__detail__answer')
            
            answers = []
            for container in answer_containers:
                try:
                    # Extract answer text
                    answer_text_elem = container.find('div', class_='askguru__detail__answer__text')
                    if not answer_text_elem:
                        continue
                    
                    # Get the text content, removing any "Read More" links
                    answer_text = answer_text_elem.get_text(strip=True)
                    
                    # Extract author information
                    author_elem = container.find('span', class_='askguru-profile__name')
                    author = author_elem.get_text(strip=True) if author_elem else "Unknown"
                    
                    # Extract date
                    date_elem = container.find('time')
                    date = date_elem.get('datetime', '') if date_elem else ""
                    
                    # Extract upvotes
                    upvote_elem = container.find('span', class_='ag-answer-upvote-count-message')
                    upvotes = upvote_elem.get_text(strip=True) if upvote_elem else "0"
                    
                    # Extract answer ID
                    answer_id = container.get('id', '').replace('answer-', '') if container.get('id') else ""
                    
                    answer_data = {
                        'answer_id': answer_id,
                        'text': answer_text,
                        'author': author,
                        'date': date,
                        'upvotes': upvotes,
                        'question_url': question_url
                    }
                    
                    answers.append(answer_data)
                    
                except Exception as e:
                    print(f"Error parsing answer: {e}")
                    continue
            
            return answers
            
        except Exception as e:
            print(f"Error scraping answers for {question_url}: {e}")
            return []
    
    def _process_property_guru_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process PropertyGuru specific data format
        
        Args:
            raw_data: Raw scraped data
            
        Returns:
            Processed data with PropertyGuru specific structure
        """
        processed_data = []
        
        for item in raw_data:
            # Extract text content
            text = item.get('text', '')
            if not text.strip():
                continue
                
            # Determine if this is a question or answer based on content
            is_question = self._is_question_content(text)
            
            processed_item = {
                'source': 'propertyguru',
                'type': 'question' if is_question else 'answer',
                'text': text,
                'author': item.get('author', ''),
                'date': item.get('date', ''),
                'url': item.get('url', ''),
                'title': item.get('title', ''),
                'category': item.get('category', ''),
                'answer_count': item.get('answer_count', ''),
                'raw_data': item  # Keep original data for debugging
            }
            
            processed_data.append(processed_item)
        
        return processed_data
    
    def _is_question_content(self, text: str) -> bool:
        """
        Determine if content is a question or answer
        
        Args:
            text: Text content to analyze
            
        Returns:
            True if it's a question, False if it's an answer
        """
        # Simple heuristics to determine if content is a question
        question_indicators = [
            '?', 'how', 'what', 'when', 'where', 'why', 'which',
            'can', 'could', 'would', 'should', 'is there', 'are there'
        ]
        
        text_lower = text.lower()
        
        # Check for question marks
        if '?' in text:
            return True
            
        # Check for question words at the beginning
        for indicator in question_indicators:
            if text_lower.startswith(indicator):
                return True
                
        return False
    
    async def scrape_multiple_pages(self, max_pages: int = 3, max_questions_per_page: int = 5) -> List[Dict[str, Any]]:
        """
        Scrape multiple pages of PropertyGuru HDB questions
        
        Args:
            max_pages: Maximum number of pages to scrape
            max_questions_per_page: Maximum questions to process per page
            
        Returns:
            Combined data from all pages
        """
        all_data = []
        
        for page in range(1, max_pages + 1):
            logger.info(f"Scraping PropertyGuru page {page}")
            
            # Construct URL for each page
            page_url = f"https://www.propertyguru.com.sg/property-investment-questions/hdb-questions/{page}"
            
            # Define selectors (use the working selector)
            selectors = {
                "container": "div.question-div",  # Each question div contains all the data
                "text": "div.question-content article",  # Question text
                "author": "div.question-poster-info span.question-screen-name",  # Author name
                "date": "div.question-poster-info__sub-info",  # Date
                "category": "div.question-poster-info a.question-category",  # Category
                "answer_count": "div.question-info .answer-count",  # Answer count
                "url": "div.question-content a"  # Question URL
            }
            
            # Optimized options
            options = {
                "wait_for": 2000,  # Reduced wait time
                "wait_time": 5,  # Reduced Selenium wait time
                "screenshot": False
            }
            
            try:
                page_data = await self.helper.scrape_with_delay(
                    page_url,
                    selectors,
                    options,
                    delay=1.0  # Reduced delay
                )
                
                processed_page_data = self._process_property_guru_data(page_data)
                
                # Limit questions per page
                if max_questions_per_page and len(processed_page_data) > max_questions_per_page:
                    processed_page_data = processed_page_data[:max_questions_per_page]
                
                # Scrape answers for questions on this page
                enhanced_page_data = await self._scrape_answers_for_questions(processed_page_data)
                
                all_data.extend(enhanced_page_data)
                
                logger.info(f"Scraped {len(enhanced_page_data)} items from page {page}")
                
            except Exception as e:
                logger.error(f"Error scraping PropertyGuru page {page}: {e}")
                continue
        
        logger.info(f"Total PropertyGuru items scraped: {len(all_data)}")
        return all_data

    async def scrape_all_pages_comprehensive(self, total_pages: int = 708,
                                           resume: bool = True) -> List[Dict[str, Any]]:
        """
        Comprehensive scraper for all PropertyGuru pages with resume capability

        Args:
            total_pages: Total number of pages to scrape (default 708)
            resume: Whether to resume from previous progress

        Returns:
            List of all scraped questions and answers
        """
        logger.info(f"Starting comprehensive PropertyGuru scraping for {total_pages} pages")

        # Load previous progress if resuming
        progress = await self._load_progress() if resume else {}
        start_page = progress.get('last_completed_page', 0) + 1
        all_data = progress.get('scraped_data', [])

        logger.info(f"Starting from page {start_page}, already have {len(all_data)} questions")

        failed_pages = []

        for page in range(start_page, total_pages + 1):
            logger.info(f"Scraping page {page}/{total_pages}")

            try:
                page_data = await self._scrape_single_page_with_retry(page)

                if page_data:
                    all_data.extend(page_data)
                    logger.info(f"Page {page}: Successfully scraped {len(page_data)} questions")

                    # Save progress every batch_size questions
                    if page % self.batch_size == 0:
                        await self._save_progress(page, all_data, failed_pages)
                        logger.info(f"Progress saved at page {page}, total questions: {len(all_data)}")
                else:
                    failed_pages.append(page)
                    logger.warning(f"Page {page}: No data scraped")

                # Polite delay between pages
                await asyncio.sleep(2.0)

            except Exception as e:
                logger.error(f"Page {page}: Critical error - {e}")
                failed_pages.append(page)
                continue

        # Final save
        await self._save_progress(total_pages, all_data, failed_pages)

        # Save final output
        output_file = os.path.join(self.output_dir, f"propertyguru_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        await self._save_final_output(all_data, output_file)

        logger.info(f"Comprehensive scraping completed!")
        logger.info(f"Total questions scraped: {len(all_data)}")
        logger.info(f"Failed pages: {len(failed_pages)} - {failed_pages[:10]}{'...' if len(failed_pages) > 10 else ''}")
        logger.info(f"Final output saved to: {output_file}")

        return all_data

    async def _scrape_single_page_with_retry(self, page: int) -> List[Dict[str, Any]]:
        """
        Scrape a single page with retry logic

        Args:
            page: Page number to scrape

        Returns:
            List of questions from the page
        """
        page_url = f"{self.base_url}/{page}"

        for attempt in range(self.max_retries):
            try:
                logger.info(f"Page {page}, attempt {attempt + 1}/{self.max_retries}")

                # Define selectors
                selectors = {
                    "container": "div.question-div",
                    "text": "div.question-content article",
                    "author": "div.question-poster-info span.question-screen-name",
                    "date": "div.question-poster-info__sub-info",
                    "category": "div.question-poster-info a.question-category",
                    "answer_count": "div.question-info .answer-count",
                    "url": "div.question-content a"
                }

                # Optimized options for large-scale scraping
                options = {
                    "wait_for": 3000,
                    "wait_time": 8,
                    "screenshot": False
                }

                # Scrape the page
                scraped_data = await self.helper.scrape_with_delay(
                    page_url,
                    selectors,
                    options,
                    delay=1.5
                )

                if scraped_data and 'html' in scraped_data[0]:
                    scraped_data = self._parse_html_content(scraped_data[0]['html'])

                if not scraped_data:
                    logger.warning(f"Page {page}: No data found on attempt {attempt + 1}")
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(self.retry_delay * (attempt + 1))
                        continue
                    else:
                        return []

                # Process the data
                processed_data = self._process_property_guru_data(scraped_data)

                # Scrape answers for each question
                enhanced_data = await self._scrape_answers_for_questions(processed_data)

                return enhanced_data

            except Exception as e:
                logger.error(f"Page {page}, attempt {attempt + 1}: Error - {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    logger.error(f"Page {page}: All retry attempts failed")
                    return []

        return []

    async def _load_progress(self) -> Dict[str, Any]:
        """Load previous scraping progress"""
        try:
            if os.path.exists(self.progress_file):
                async with aiofiles.open(self.progress_file, 'r') as f:
                    content = await f.read()
                    progress = json.loads(content)
                    logger.info(f"Loaded progress: page {progress.get('last_completed_page', 0)}, "
                              f"{len(progress.get('scraped_data', []))} questions")
                    return progress
        except Exception as e:
            logger.error(f"Error loading progress: {e}")

        return {}

    async def _save_progress(self, last_page: int, data: List[Dict[str, Any]],
                           failed_pages: List[int]):
        """Save current scraping progress"""
        try:
            progress = {
                'last_completed_page': last_page,
                'scraped_data': data,
                'failed_pages': failed_pages,
                'timestamp': datetime.now().isoformat(),
                'total_questions': len(data)
            }

            async with aiofiles.open(self.progress_file, 'w') as f:
                await f.write(json.dumps(progress, indent=2, ensure_ascii=False))

        except Exception as e:
            logger.error(f"Error saving progress: {e}")

    async def _save_final_output(self, data: List[Dict[str, Any]], output_file: str):
        """Save final comprehensive output"""
        try:
            # Add metadata
            output = {
                'metadata': {
                    'source': 'PropertyGuru HDB Questions',
                    'scraping_date': datetime.now().isoformat(),
                    'total_questions': len(data),
                    'total_answers': sum(len(q.get('answers', [])) for q in data),
                    'scraper_version': '2.0_comprehensive'
                },
                'questions': data
            }

            async with aiofiles.open(output_file, 'w') as f:
                await f.write(json.dumps(output, indent=2, ensure_ascii=False))

            logger.info(f"Final output saved: {len(data)} questions, "
                       f"{sum(len(q.get('answers', [])) for q in data)} answers")

        except Exception as e:
            logger.error(f"Error saving final output: {e}")

    async def retry_failed_pages(self, failed_pages: List[int] = None) -> List[Dict[str, Any]]:
        """
        Retry scraping failed pages from previous run

        Args:
            failed_pages: List of page numbers to retry, or None to load from progress

        Returns:
            List of successfully scraped questions
        """
        if failed_pages is None:
            progress = await self._load_progress()
            failed_pages = progress.get('failed_pages', [])

        if not failed_pages:
            logger.info("No failed pages to retry")
            return []

        logger.info(f"Retrying {len(failed_pages)} failed pages")

        recovered_data = []
        still_failed = []

        for page in failed_pages:
            logger.info(f"Retrying page {page}")

            try:
                page_data = await self._scrape_single_page_with_retry(page)

                if page_data:
                    recovered_data.extend(page_data)
                    logger.info(f"Successfully recovered page {page}: {len(page_data)} questions")
                else:
                    still_failed.append(page)
                    logger.warning(f"Page {page} still failed after retry")

                await asyncio.sleep(2.0)

            except Exception as e:
                logger.error(f"Error retrying page {page}: {e}")
                still_failed.append(page)

        logger.info(f"Recovery complete: {len(recovered_data)} questions recovered, "
                   f"{len(still_failed)} pages still failed")

        return recovered_data

    def get_scraping_stats(self) -> Dict[str, Any]:
        """Get current scraping statistics"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r') as f:
                    progress = json.load(f)

                data = progress.get('scraped_data', [])

                stats = {
                    'last_completed_page': progress.get('last_completed_page', 0),
                    'total_questions': len(data),
                    'total_answers': sum(len(q.get('answers', [])) for q in data),
                    'failed_pages': len(progress.get('failed_pages', [])),
                    'last_update': progress.get('timestamp', 'Unknown'),
                    'completion_percentage': (progress.get('last_completed_page', 0) / 708) * 100
                }

                return stats
        except Exception as e:
            logger.error(f"Error getting stats: {e}")

        return {'error': 'No progress data found'}

# Convenience functions for main.py and external use
async def scrape_property_guru(max_questions: int = 5) -> List[Dict[str, Any]]:
    """Convenience function to scrape PropertyGuru (limited for testing)"""
    scraper = PropertyGuruScraper()
    return await scraper.scrape_property_guru(max_questions)

async def scrape_property_guru_comprehensive(total_pages: int = 708,
                                           resume: bool = True) -> List[Dict[str, Any]]:
    """
    Comprehensive PropertyGuru scraper for all pages

    Args:
        total_pages: Total number of pages to scrape (default 708)
        resume: Whether to resume from previous progress

    Returns:
        List of all scraped questions and answers
    """
    scraper = PropertyGuruScraper()
    return await scraper.scrape_all_pages_comprehensive(total_pages, resume)

async def retry_failed_property_guru_pages(failed_pages: List[int] = None) -> List[Dict[str, Any]]:
    """
    Retry failed PropertyGuru pages

    Args:
        failed_pages: List of page numbers to retry, or None to load from progress

    Returns:
        List of successfully scraped questions
    """
    scraper = PropertyGuruScraper()
    return await scraper.retry_failed_pages(failed_pages)

def get_property_guru_stats() -> Dict[str, Any]:
    """Get PropertyGuru scraping statistics"""
    scraper = PropertyGuruScraper()
    return scraper.get_scraping_stats()