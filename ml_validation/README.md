# ML Validation: Singapore BERT vs Baseline BERT

## 🎯 Project Goal
Test Singapore-localized BERT models against baseline BERT using PropertyGuru HDB Q&A dataset to determine which performs better for Singapore context.

## 📁 Project Structure
```
ml_validation/
├── PROJECT_PLAN.md          # Detailed project plan
├── README.md               # This file
├── notebooks/              # Jupyter notebooks for Colab
├── data/                   # Dataset and preprocessed files
├── results/                # Model outputs and metrics
└── docs/                   # Documentation and reports
```

## 📊 Dataset
- **Source**: PropertyGuru HDB Questions & Answers
- **Size**: 2,400 questions with up to 20 answers each
- **Format**: CSV with simple structure
- **Domain**: Singapore housing (HDB, BTO, resale market)

## 🤖 Models to Compare
### Singapore BERT Models
- Singapore-specific BERT (TBD)
- Southeast Asia BERT variants
- Fine-tuned BERT on Singapore text

### Baseline Models  
- BERT-base-uncased
- BERT-base-cased
- RoBERTa-base

## 🧪 Evaluation Tasks
1. **Question Classification** - Categorize by HDB topics
2. **Answer Quality Prediction** - Predict helpful answers
3. **Question-Answer Matching** - Semantic similarity
4. **Singapore Context Understanding** - Local term comprehension

## 🚀 Quick Start
1. Open Google Colab: https://colab.research.google.com/drive/17okGbmNwOakgfU9mGE3TqDxQ72_tbaGq?usp=sharing
2. Upload dataset from `../data/final/propertyguru_SIMPLE_20250716_043050.csv`
3. Follow notebooks in order:
   - `01_data_preparation.ipynb`
   - `02_baseline_bert.ipynb` 
   - `03_singapore_bert.ipynb`
   - `04_comparison_analysis.ipynb`

## 📈 Success Metrics
- Classification accuracy improvement
- Better understanding of Singapore terms
- Higher semantic similarity scores
- Reduced perplexity on local context

## 📞 Discussion Points
- Which Singapore BERT models to test?
- Priority evaluation tasks?
- Performance improvement thresholds?
- Timeline and resource requirements?

**Ready to validate Singapore BERT superiority!** 🇸🇬🤖
