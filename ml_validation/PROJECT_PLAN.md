# ML Validation Project: Singapore BERT vs Baseline BERT

## 🎯 Project Overview

**Objective**: Test and compare the quality of Singapore-localized BERT models against baseline BERT models using PropertyGuru HDB Q&A dataset.

**Platform**: Google Colab  
**Dataset**: 2,400 PropertyGuru HDB questions with answers  
**Timeline**: 1-2 weeks  

## 📊 Dataset Summary

### **Phase 1: PropertyGuru Dataset (Immediate)**
- **File**: `../data/final/propertyguru_SIMPLE_20250716_043050.csv`
- **Format**: `question, answer1, answer2, ..., answer20`
- **Total Questions**: 2,400
- **High-Quality Subset**: 1,427 questions (2+ answers)
- **Domain**: Singapore HDB housing questions
- **Language**: Formal English with Singapore context

### **Phase 2: Reddit Dataset (Parallel Collection)**
- **Sources**: r/singapore, r/asksingapore, r/singaporefi
- **Expected Size**: 3,000-7,000 additional questions
- **Domain**: Broader Singapore housing (HDB + private + rental)
- **Language**: Casual English with more Singlish expressions

### **Phase 3: Combined Dataset**
- **Total Expected**: 5,400-9,400 questions
- **Diversity**: Formal + casual language styles
- **Coverage**: Comprehensive Singapore housing context

### **Data Characteristics**
- **Local Context**: Singapore-specific housing terms (HDB, BTO, COV, etc.)
- **Mixed Language**: English with Singlish expressions
- **Real-World**: Actual user questions from multiple platforms
- **Diverse Answers**: Expert responses + community discussions

## 🤖 Models to Test

### **Singapore-Localized BERT Models**
1. **Primary Candidate**: Singapore-specific BERT (if available)
2. **Alternative**: Southeast Asia BERT models
3. **Backup**: Fine-tuned BERT on Singapore text

### **Baseline Models**
1. **BERT-base-uncased** (Google's original)
2. **BERT-base-cased** 
3. **RoBERTa-base** (for comparison)

## 🧪 Evaluation Tasks

### **Task 1: Question Classification**
- **Objective**: Classify questions by HDB topic categories
- **Categories**: 
  - BTO/New Flats
  - Resale Market
  - Renovation/Interior
  - Financial/Grants
  - Legal/Procedures
  - General Housing

### **Task 2: Answer Quality Prediction**
- **Objective**: Predict which answers are most helpful
- **Features**: Answer text, length, context
- **Target**: Upvotes/engagement scores

### **Task 3: Question-Answer Matching**
- **Objective**: Match questions to most relevant answers
- **Method**: Semantic similarity scoring
- **Evaluation**: Ranking accuracy

### **Task 4: Singapore Context Understanding**
- **Objective**: Test understanding of local terms
- **Method**: Masked language modeling on Singapore-specific terms
- **Examples**: HDB, BTO, COV, CPF, etc.

## 📈 Evaluation Metrics

### **Classification Tasks**
- **Accuracy**: Overall classification accuracy
- **F1-Score**: Macro and weighted F1
- **Precision/Recall**: Per-class performance
- **Confusion Matrix**: Error analysis

### **Regression Tasks** (Answer Quality)
- **MSE**: Mean Squared Error
- **MAE**: Mean Absolute Error
- **R²**: Coefficient of determination
- **Spearman Correlation**: Rank correlation

### **Similarity Tasks**
- **Cosine Similarity**: Semantic similarity scores
- **Ranking Metrics**: MRR, NDCG
- **Top-K Accuracy**: Retrieval performance

### **Singapore Context**
- **Perplexity**: Language modeling performance
- **Token Accuracy**: Masked token prediction
- **Context Coherence**: Qualitative assessment

## 🔬 Experimental Design

### **Phase 1: Initial Data Preparation** (Day 1-2)
1. **PropertyGuru Data Loading**: Import CSV to Colab
2. **Preprocessing**: Clean and tokenize text
3. **Label Creation**: Generate classification labels
4. **Train/Val/Test Split**: 70/15/15 split
5. **Data Analysis**: Explore Singapore-specific terms

### **Phase 2: Reddit Data Collection** (Day 2-3, Parallel)
1. **Reddit Crawler Setup**: Configure for Singapore housing subreddits
2. **Data Collection**: Scrape r/singapore, r/asksingapore, r/singaporefi
3. **Data Cleaning**: Process Reddit posts into Q&A format
4. **Quality Filtering**: Remove low-quality posts
5. **Format Standardization**: Match PropertyGuru CSV structure

### **Phase 3: Baseline Evaluation (Small Dataset)** (Day 3-4)
1. **BERT-base Setup**: Load and configure baseline models
2. **Fine-tuning**: Adapt to PropertyGuru dataset (2,400 questions)
3. **Task Evaluation**: Run all 4 evaluation tasks
4. **Performance Baseline**: Establish comparison metrics

### **Phase 4: Singapore BERT Testing (Small Dataset)** (Day 5-6)
1. **Model Selection**: Identify best Singapore BERT
2. **Fine-tuning**: Adapt to PropertyGuru dataset
3. **Task Evaluation**: Run identical evaluation tasks
4. **Performance Comparison**: Compare against baseline

### **Phase 5: Combined Dataset Evaluation** (Day 7-9)
1. **Data Merging**: Combine PropertyGuru + Reddit datasets
2. **Re-training**: Both baseline and Singapore BERT on larger dataset
3. **Comparative Analysis**: Small vs large dataset performance
4. **Improvement Measurement**: Quantify benefits of more data

### **Phase 6: Analysis & Optimization** (Day 10-12)
1. **Error Analysis**: Identify failure cases
2. **Hyperparameter Tuning**: Optimize both models
3. **Ablation Studies**: Test different configurations
4. **Statistical Testing**: Significance tests

### **Phase 7: Reporting** (Day 13-14)
1. **Results Compilation**: Aggregate all metrics
2. **Visualization**: Create comparison charts
3. **Case Studies**: Analyze specific examples
4. **Recommendations**: Provide actionable insights

## 🛠️ Technical Implementation

### **Google Colab Setup**
```python
# Key libraries
!pip install transformers torch datasets scikit-learn
!pip install seaborn matplotlib plotly

# Singapore BERT model (example)
from transformers import AutoTokenizer, AutoModel
tokenizer = AutoTokenizer.from_pretrained("singapore-bert-model")
model = AutoModel.from_pretrained("singapore-bert-model")
```

### **Data Pipeline**
1. **CSV Loading**: pandas.read_csv()
2. **Text Preprocessing**: Clean, tokenize, encode
3. **Label Generation**: Automated categorization
4. **Dataset Creation**: HuggingFace datasets format

### **Model Training**
1. **Fine-tuning**: Task-specific heads
2. **Training Loop**: Custom or HuggingFace Trainer
3. **Validation**: Early stopping, best model selection
4. **Evaluation**: Comprehensive metrics calculation

## 📋 Deliverables

### **Code Deliverables**
1. **Data Preprocessing Notebook**: Clean and prepare dataset
2. **Baseline Model Notebook**: BERT-base evaluation
3. **Singapore BERT Notebook**: Localized model evaluation
4. **Comparison Analysis Notebook**: Side-by-side results
5. **Visualization Notebook**: Charts and insights

### **Documentation**
1. **Technical Report**: Detailed methodology and results
2. **Executive Summary**: Key findings and recommendations
3. **Model Cards**: Documentation for each model tested
4. **Reproducibility Guide**: Steps to replicate results

### **Results**
1. **Performance Metrics**: Comprehensive comparison table
2. **Statistical Analysis**: Significance tests and confidence intervals
3. **Error Analysis**: Failure cases and improvement suggestions
4. **Recommendations**: Which model to use for Singapore context

## 🎯 Success Criteria

### **Primary Goals**
- [ ] Successfully evaluate both baseline and Singapore BERT models
- [ ] Generate comprehensive performance comparison
- [ ] Identify clear winner for Singapore HDB domain
- [ ] Provide actionable recommendations

### **Secondary Goals**
- [ ] Discover Singapore-specific linguistic patterns
- [ ] Create reusable evaluation framework
- [ ] Generate insights for future model development
- [ ] Build foundation for production deployment

## 🚀 Next Steps

### **Immediate Actions**
1. **Review Colab Notebook**: Examine existing setup
2. **Data Transfer**: Upload PropertyGuru dataset to Colab
3. **Model Research**: Identify best Singapore BERT candidates
4. **Environment Setup**: Configure Colab with required libraries

### **Discussion Points**
1. **Model Selection**: Which Singapore BERT models to test?
2. **Evaluation Focus**: Which tasks are most important?
3. **Success Metrics**: What constitutes "better" performance?
4. **Timeline**: Is 1-2 weeks realistic for comprehensive evaluation?
5. **Resources**: GPU requirements and Colab Pro needs?

## 📞 Let's Discuss

**Key Questions for Discussion:**
1. Do you have specific Singapore BERT models in mind?
2. Which evaluation tasks are highest priority?
3. Should we focus on classification or similarity tasks?
4. Any specific Singapore terms/contexts to emphasize?
5. What's the target performance improvement threshold?

**Ready to dive into the ML validation!** 🤖
