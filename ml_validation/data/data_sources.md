# Data Sources for ML Validation

## 📊 Primary Dataset

### **PropertyGuru HDB Q&A Dataset**
- **File**: `../../data/final/propertyguru_SIMPLE_20250716_043050.csv`
- **Format**: `question, answer1, answer2, ..., answer20`
- **Size**: 2,400 questions
- **Quality**: 1,427 questions with 2+ answers

### **High-Quality Subset**
- **File**: `../../data/final/propertyguru_SIMPLE_HQ_20250716_043050.csv`  
- **Size**: 1,427 questions (filtered for 2+ answers)
- **Use**: Primary evaluation dataset

## 🏷️ Labels to Generate

### **Question Categories** (for classification)
1. **BTO/New Flats** - Build-to-Order applications, balloting
2. **Resale Market** - Buying/selling existing flats, COV
3. **Renovation/Interior** - Home improvement, contractors
4. **Financial/Grants** - CPF, housing loans, subsidies
5. **Legal/Procedures** - HDB rules, regulations, processes
6. **General Housing** - Maintenance, utilities, neighbors

### **Answer Quality Scores** (for regression)
- Based on answer length, completeness, helpfulness indicators
- Proxy metrics from engagement patterns

## 🇸🇬 Singapore-Specific Terms to Test

### **Housing Terms**
- HDB, BTO, SBF, COV, TOP, MOP
- Resale levy, ethnic integration policy
- Executive flat, DBSS, EC

### **Financial Terms**  
- CPF, OA, SA, housing grant
- Medisave, SRS, CPFIS

### **Local Context**
- Void deck, wet market, hawker center
- MRT, LRT, bus interchange
- Singlish expressions in housing context

## 📁 File Organization
```
data/
├── raw/                    # Original CSV files
├── processed/              # Cleaned and labeled data
├── splits/                 # Train/val/test splits
└── singapore_terms/        # Singapore-specific vocabulary
```

## 🔄 Data Pipeline
1. **Load** original CSV from parent project
2. **Clean** text and handle missing values
3. **Label** questions with categories
4. **Split** into train/validation/test sets
5. **Tokenize** for BERT input format
6. **Save** processed datasets for model training
