# ML Validation Notebooks

## 📚 Notebook Sequence

### **01_data_preparation.ipynb**
- Load PropertyGuru dataset
- Explore Singapore-specific terms
- Create classification labels
- Preprocess text for BERT
- Train/validation/test splits

### **02_baseline_bert.ipynb** 
- Load BERT-base models
- Fine-tune on PropertyGuru data
- Evaluate on all 4 tasks
- Establish baseline metrics

### **03_singapore_bert.ipynb**
- Load Singapore BERT models
- Fine-tune on PropertyGuru data  
- Evaluate on identical tasks
- Compare against baseline

### **04_comparison_analysis.ipynb**
- Side-by-side performance comparison
- Statistical significance testing
- Error analysis and case studies
- Visualization and insights

### **05_singapore_context_deep_dive.ipynb**
- Focus on Singapore-specific terms
- Masked language modeling tests
- Qualitative analysis
- Recommendations

## 🚀 Usage
1. Upload to Google Colab
2. Run notebooks in sequence
3. Each notebook saves results to `../results/`
4. Final analysis in notebook 04

## 📊 Expected Outputs
- Performance comparison tables
- Visualization charts
- Model evaluation metrics
- Singapore context analysis
- Recommendations report
