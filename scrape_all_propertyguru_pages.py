#!/usr/bin/env python3
"""
Simple PropertyGuru All Pages Scraper
Uses the EXACT same working method from demo, just loops through all 708 pages
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from mvp_scraper.property_guru_scraper import PropertyGuruScraper

async def scrape_all_pages():
    """Scrape all PropertyGuru pages using the working method"""
    
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru ALL PAGES Scraper                           ║
║                                                                              ║
║  Using the EXACT same method that created your successful demo data         ║
║  Will scrape all 708 pages (~14,000+ questions with answers)               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    scraper = PropertyGuruScraper()
    
    # Create output directory
    output_dir = "data/processed"
    os.makedirs(output_dir, exist_ok=True)
    
    # Progress tracking
    all_questions = []
    failed_pages = []
    
    start_time = datetime.now()
    
    print(f"🚀 Starting comprehensive scraping at {start_time}")
    print(f"📊 Target: 708 pages")
    print(f"⏱️  Estimated time: 12-24 hours")
    print(f"💾 Progress will be saved every 50 questions")
    print()
    
    # TEST: Just 2 pages first, then change to 709 for full run
    for page in range(1, 3):  # Pages 1 to 2 (TEST)
        try:
            print(f"📄 Scraping page {page}/708 ({(page/708)*100:.1f}% complete)")
            
            # Use the EXACT same method that works in demo
            # Temporarily modify the base URL for this page
            original_url = scraper.base_url
            scraper.base_url = f"https://www.propertyguru.com.sg/property-investment-questions/hdb-questions/{page}"

            # Scrape this single page (same as demo method)
            page_questions = await scraper.scrape_property_guru(max_questions=None)

            # Restore original URL
            scraper.base_url = original_url
            
            if page_questions:
                all_questions.extend(page_questions)
                print(f"   ✅ Success: {len(page_questions)} questions scraped")
                print(f"   📈 Total so far: {len(all_questions)} questions")
            else:
                failed_pages.append(page)
                print(f"   ❌ Failed: No data from page {page}")
            
            # Save progress every 50 questions
            if len(all_questions) % 50 == 0 and len(all_questions) > 0:
                progress_file = os.path.join(output_dir, f"propertyguru_progress_{len(all_questions)}.json")
                with open(progress_file, 'w') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'pages_completed': page,
                        'total_questions': len(all_questions),
                        'failed_pages': failed_pages,
                        'questions': all_questions
                    }, f, indent=2)
                print(f"   💾 Progress saved: {len(all_questions)} questions")
            
            # Polite delay
            await asyncio.sleep(2)
            
        except KeyboardInterrupt:
            print(f"\n⏸️  Scraping interrupted by user at page {page}")
            break
            
        except Exception as e:
            print(f"   ❌ Error on page {page}: {e}")
            failed_pages.append(page)
            continue
    
    # Final save
    end_time = datetime.now()
    duration = end_time - start_time
    
    final_output = {
        'metadata': {
            'scraping_start': start_time.isoformat(),
            'scraping_end': end_time.isoformat(),
            'duration_seconds': duration.total_seconds(),
            'total_questions': len(all_questions),
            'total_answers': sum(len(q.get('answers', [])) for q in all_questions),
            'failed_pages': len(failed_pages),
            'success_rate': ((708 - len(failed_pages)) / 708) * 100
        },
        'questions': all_questions
    }
    
    # Save final comprehensive file
    final_file = os.path.join(output_dir, f"propertyguru_complete_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(final_file, 'w') as f:
        json.dump(final_output, f, indent=2)
    
    print(f"\n🎉 SCRAPING COMPLETED!")
    print(f"📊 Final Statistics:")
    print(f"   • Total questions: {len(all_questions):,}")
    print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in all_questions):,}")
    print(f"   • Duration: {duration}")
    print(f"   • Failed pages: {len(failed_pages)}")
    print(f"   • Success rate: {((708 - len(failed_pages)) / 708) * 100:.1f}%")
    print(f"   • Output file: {final_file}")
    
    if failed_pages:
        print(f"   • Failed pages: {failed_pages[:10]}{'...' if len(failed_pages) > 10 else ''}")

if __name__ == "__main__":
    try:
        asyncio.run(scrape_all_pages())
    except KeyboardInterrupt:
        print("\n👋 Scraping stopped by user")
    except Exception as e:
        print(f"\n💥 Critical error: {e}")
        print("Check the progress files in data/processed/ to resume later")
