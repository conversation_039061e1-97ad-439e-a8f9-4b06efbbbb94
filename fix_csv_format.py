#!/usr/bin/env python3
"""
Fix CSV Format - Separate Answer Columns
Creates proper CSV with answer_1, answer_2, answer_3... columns instead of combined answers
"""

import json
import os
from datetime import datetime
import re

def clean_text_for_csv(text: str) -> str:
    """Clean text for CSV format"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove HTML tags if any
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove URLs from text content
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    
    # Remove email addresses from text content
    text = re.sub(r'\S+@\S+', '', text)
    
    # Remove phone numbers
    text = re.sub(r'[\+]?[1-9]?[0-9]{7,15}', '', text)
    
    # Clean up extra spaces and newlines for CSV
    text = re.sub(r'\s+', ' ', text.strip())
    text = text.replace('\n', ' ').replace('\r', ' ')
    
    # Escape quotes for CSV
    text = text.replace('"', '""')
    
    return text

def create_proper_csv():
    """Create properly formatted CSV with separate answer columns"""
    
    # Load the ML dataset
    output_dir = "data/processed"
    ml_file = None
    
    # Find the most recent ML file
    for file in os.listdir(output_dir):
        if 'propertyguru_ML_READY' in file and file.endswith('.json'):
            ml_file = os.path.join(output_dir, file)
            break
    
    if not ml_file:
        print("❌ No ML dataset found. Please run prepare_ml_dataset.py first.")
        return None
    
    print(f"📊 Loading ML data from: {ml_file}")
    
    with open(ml_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    ml_records = data.get('data', [])
    print(f"📈 Processing {len(ml_records)} records...")
    
    # Find maximum number of answers to determine column count
    max_answers = 0
    for record in ml_records:
        raw_answers = record.get('raw_answers', [])
        if len(raw_answers) > max_answers:
            max_answers = len(raw_answers)
    
    print(f"📊 Maximum answers per question: {max_answers}")
    print(f"🔄 Creating CSV with separate answer columns...")
    
    # Create CSV with proper headers
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_file = os.path.join(output_dir, f"propertyguru_ML_PROPER_{timestamp}.csv")
    
    # Define headers
    base_headers = [
        'question_id',
        'question_text',
        'question_length',
        'question_word_count',
        'answer_count',
        'total_upvotes',
        'max_upvotes',
        'engagement_score',
        'quality_score',
        'author',
        'date',
        'is_anonymous',
        'has_answers',
        'has_upvoted_answers',
        'source_file'
    ]
    
    # Add answer columns
    answer_headers = []
    for i in range(1, max_answers + 1):
        answer_headers.extend([
            f'answer_{i}_text',
            f'answer_{i}_author', 
            f'answer_{i}_upvotes',
            f'answer_{i}_date'
        ])
    
    all_headers = base_headers + answer_headers
    
    print(f"📋 CSV will have {len(all_headers)} columns")
    
    # Write CSV
    with open(csv_file, 'w', encoding='utf-8') as f:
        # Write headers
        f.write(','.join(f'"{h}"' for h in all_headers) + '\n')
        
        # Write data
        for i, record in enumerate(ml_records):
            if i % 500 == 0:
                print(f"   Processing record {i+1}/{len(ml_records)}...")
            
            row = []
            
            # Base data
            row.append(clean_text_for_csv(record.get('question_id', '')))
            row.append(clean_text_for_csv(record.get('question_text', '')))
            row.append(str(record.get('question_length', 0)))
            row.append(str(record.get('question_word_count', 0)))
            row.append(str(record.get('answer_count', 0)))
            row.append(str(record.get('total_upvotes', 0)))
            row.append(str(record.get('max_upvotes', 0)))
            row.append(str(record.get('engagement_score', 0)))
            row.append(str(record.get('quality_score', 0)))
            row.append(clean_text_for_csv(record.get('author', '')))
            row.append(clean_text_for_csv(record.get('date', '')))
            row.append(str(record.get('is_anonymous', False)))
            row.append(str(record.get('has_answers', False)))
            row.append(str(record.get('has_upvoted_answers', False)))
            row.append(clean_text_for_csv(record.get('source_file', '')))
            
            # Answer data
            raw_answers = record.get('raw_answers', [])
            
            for i in range(max_answers):
                if i < len(raw_answers):
                    answer = raw_answers[i]
                    row.append(clean_text_for_csv(answer.get('text', '')))
                    row.append(clean_text_for_csv(answer.get('author', '')))
                    row.append(str(answer.get('upvotes', 0)))
                    row.append(clean_text_for_csv(answer.get('date', '')))
                else:
                    # Empty columns for missing answers
                    row.extend(['', '', '0', ''])
            
            # Write row
            f.write(','.join(f'"{cell}"' for cell in row) + '\n')
    
    # Create high-quality subset
    hq_csv_file = os.path.join(output_dir, f"propertyguru_HIGH_QUALITY_PROPER_{timestamp}.csv")
    
    print(f"🔄 Creating high-quality subset...")
    
    # Calculate quality threshold (top 20%)
    quality_scores = [r.get('quality_score', 0) for r in ml_records]
    quality_scores_sorted = sorted(quality_scores)
    q80_index = int(len(quality_scores_sorted) * 0.8)
    quality_threshold = quality_scores_sorted[q80_index] if quality_scores_sorted else 0
    
    high_quality_records = [r for r in ml_records if r.get('quality_score', 0) > quality_threshold]
    
    with open(hq_csv_file, 'w', encoding='utf-8') as f:
        # Write headers
        f.write(','.join(f'"{h}"' for h in all_headers) + '\n')
        
        # Write high-quality data
        for record in high_quality_records:
            row = []
            
            # Base data
            row.append(clean_text_for_csv(record.get('question_id', '')))
            row.append(clean_text_for_csv(record.get('question_text', '')))
            row.append(str(record.get('question_length', 0)))
            row.append(str(record.get('question_word_count', 0)))
            row.append(str(record.get('answer_count', 0)))
            row.append(str(record.get('total_upvotes', 0)))
            row.append(str(record.get('max_upvotes', 0)))
            row.append(str(record.get('engagement_score', 0)))
            row.append(str(record.get('quality_score', 0)))
            row.append(clean_text_for_csv(record.get('author', '')))
            row.append(clean_text_for_csv(record.get('date', '')))
            row.append(str(record.get('is_anonymous', False)))
            row.append(str(record.get('has_answers', False)))
            row.append(str(record.get('has_upvoted_answers', False)))
            row.append(clean_text_for_csv(record.get('source_file', '')))
            
            # Answer data
            raw_answers = record.get('raw_answers', [])
            
            for i in range(max_answers):
                if i < len(raw_answers):
                    answer = raw_answers[i]
                    row.append(clean_text_for_csv(answer.get('text', '')))
                    row.append(clean_text_for_csv(answer.get('author', '')))
                    row.append(str(answer.get('upvotes', 0)))
                    row.append(clean_text_for_csv(answer.get('date', '')))
                else:
                    row.extend(['', '', '0', ''])
            
            f.write(','.join(f'"{cell}"' for cell in row) + '\n')
    
    # Generate statistics
    answered_questions = [r for r in ml_records if r.get('has_answers', False)]
    upvoted_questions = [r for r in ml_records if r.get('has_upvoted_answers', False)]
    
    print(f"\n🎉 PROPER CSV FORMAT CREATED!")
    print(f"📊 Dataset Statistics:")
    print(f"   • Total records: {len(ml_records)}")
    print(f"   • Questions with answers: {len(answered_questions)}")
    print(f"   • Questions with upvotes: {len(upvoted_questions)}")
    print(f"   • High-quality records: {len(high_quality_records)}")
    print(f"   • Maximum answers per question: {max_answers}")
    print(f"   • Total CSV columns: {len(all_headers)}")
    
    print(f"\n📁 Output Files:")
    print(f"   • Full Dataset (Proper CSV): {csv_file}")
    print(f"   • High Quality Subset (Proper CSV): {hq_csv_file}")
    
    print(f"\n🤖 CSV Format:")
    print(f"   • Base columns: {len(base_headers)}")
    print(f"   • Answer columns: {len(answer_headers)} (answer_1_text, answer_1_author, etc.)")
    print(f"   • Each answer has: text, author, upvotes, date")
    
    return {
        'csv_file': csv_file,
        'hq_csv_file': hq_csv_file,
        'total_records': len(ml_records),
        'max_answers': max_answers,
        'total_columns': len(all_headers)
    }

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru CSV Format Fix                              ║
║                                                                              ║
║  🎯 Creating proper CSV with separate answer columns                        ║
║  📊 Format: answer_1_text, answer_1_author, answer_2_text, etc.             ║
║  🤖 Perfect for ML analysis and validation                                  ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    try:
        result = create_proper_csv()
        if result:
            print(f"\n✅ CSV format fixed successfully!")
            print(f"🚀 Ready for ML validation with proper answer columns!")
        
    except Exception as e:
        print(f"\n💥 Error fixing CSV format: {e}")
        import traceback
        traceback.print_exc()
