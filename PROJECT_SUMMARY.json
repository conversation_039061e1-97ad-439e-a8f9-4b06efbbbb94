{"project": "PropertyGuru HDB Questions Scraper", "description": "Comprehensive scraping and ML dataset preparation for PropertyGuru HDB questions and answers", "created": "2025-07-16T04:30:01.540088", "structure": {"scrapers/": {"description": "Main scraping tools", "files": {"distributed_propertyguru_scraper.py": "3-terminal distributed scraper (RECOMMENDED)", "comprehensive_propertyguru_scraper.py": "Sequential comprehensive scraper", "parallel_propertyguru_scraper.py": "Multi-threaded parallel scraper", "start_distributed_scraping.sh": "Auto-launch script for distributed scraping"}}, "data/final/": {"description": "Final ML-ready datasets", "files": {"propertyguru_ML_PROPER_20250716_042751.csv": "Main ML dataset (2,400 questions, proper CSV format)", "propertyguru_HIGH_QUALITY_PROPER_20250716_042751.csv": "High-quality subset (479 questions)", "propertyguru_ALL_COMBINED_20250715_135521.json": "Complete JSON dataset with metadata", "propertyguru_TRAINING_PAIRS_20250716_042457.json": "Question-answer pairs for training"}}, "mvp_scraper/": {"description": "Core scraper package", "files": {"property_guru_scraper.py": "PropertyGuru scraping logic", "reddit_scraper.py": "Reddit scraping logic", "hardware_zone_scraper.py": "HardwareZone scraping logic", "scraper_helper.py": "Common scraping utilities", "gemini_processor.py": "AI processing utilities"}}, "scripts/": {"description": "Utility and legacy scripts", "legacy/": "Development scripts used during project", "utilities/": "Helper tools and demos"}, "docs/": {"description": "Project documentation", "files": {"PROJECT_PLAN.md": "Original project plan and requirements"}}}, "dataset_stats": {"total_questions": 2400, "questions_with_answers": 2056, "high_quality_questions": 479, "date_range": "April 2013 - September 2024", "avg_answers_per_question": 3.0, "csv_columns": 27, "max_answers_per_question": 3}, "usage": {"recommended_scraper": "scrapers/distributed_propertyguru_scraper.py", "main_dataset": "data/final/propertyguru_ML_PROPER_20250716_042751.csv", "high_quality_subset": "data/final/propertyguru_HIGH_QUALITY_PROPER_20250716_042751.csv", "training_pairs": "data/final/propertyguru_TRAINING_PAIRS_20250716_042457.json"}, "next_steps": ["Use data/final/ datasets for ML validation", "Test Reddit scraping with similar approach", "Implement ML models for quality prediction", "Create recommendation system using the data"]}