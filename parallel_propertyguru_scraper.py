#!/usr/bin/env python3
"""
Parallel PropertyGuru Scraper - All 708 Pages with Concurrent Processing
Features:
- Parallel processing with configurable concurrency (default: 5 concurrent pages)
- Resume capability from last successful page
- Enhanced error handling with retries per thread
- Progress monitoring and statistics
- No testing limits - scrapes all questions per page
- Much faster than sequential version (5-10x speedup)
"""

import asyncio
import json
import os
import sys
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import threading
from mvp_scraper.property_guru_scraper import PropertyGuruScraper

class ParallelPropertyGuruScraper:
    def __init__(self, output_dir: str = "data/processed", max_workers: int = 5):
        self.output_dir = output_dir
        self.progress_file = os.path.join(output_dir, "parallel_progress.json")
        self.max_workers = max_workers  # Number of concurrent pages
        self.batch_size = 20  # Save progress every 20 pages
        self.max_retries = 3
        self.retry_delay = 5.0
        
        # Thread-safe data structures
        self.lock = threading.Lock()
        self.completed_pages = set()
        self.failed_pages = set()
        self.all_questions = []
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Statistics tracking
        self.stats = {
            'start_time': None,
            'last_update': None,
            'total_questions': 0,
            'total_answers': 0,
            'pages_completed': 0,
            'pages_failed': [],
            'estimated_completion': None
        }

    def scrape_single_page_sync(self, page_num: int) -> tuple[int, List[Dict[str, Any]]]:
        """Synchronous wrapper for scraping a single page (for ThreadPoolExecutor)"""
        
        for attempt in range(self.max_retries):
            try:
                print(f"   🔄 Page {page_num}, attempt {attempt + 1}/{self.max_retries} [Thread-{threading.current_thread().ident}]")
                
                # Create scraper instance (each thread gets its own)
                scraper = PropertyGuruScraper()
                
                # Modify URL for specific page
                original_url = scraper.base_url
                scraper.base_url = f"https://www.propertyguru.com.sg/property-investment-questions/hdb-questions/{page_num}"
                
                try:
                    # Run the async scraper in this thread's event loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    # Scrape with NO LIMITS - get all questions on the page
                    questions = loop.run_until_complete(scraper.scrape_property_guru(max_questions=None))
                    
                    if questions:
                        total_answers = sum(len(q.get('answers', [])) for q in questions)
                        print(f"   ✅ Page {page_num}: {len(questions)} questions, {total_answers} answers")
                        return page_num, questions
                    else:
                        print(f"   ⚠️  Page {page_num}: No data found on attempt {attempt + 1}")
                        if attempt < self.max_retries - 1:
                            import time
                            time.sleep(self.retry_delay * (attempt + 1))
                            continue
                        else:
                            return page_num, []
                            
                finally:
                    # Restore original URL and cleanup
                    scraper.base_url = original_url
                    loop.close()
                    
            except Exception as e:
                print(f"   ❌ Page {page_num}, attempt {attempt + 1}: {e}")
                if attempt < self.max_retries - 1:
                    import time
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                else:
                    print(f"   💥 Page {page_num}: All retry attempts failed")
                    return page_num, []
        
        return page_num, []

    def process_page_result(self, page_num: int, questions: List[Dict[str, Any]]):
        """Thread-safe processing of page results"""
        with self.lock:
            if questions:
                self.all_questions.extend(questions)
                self.completed_pages.add(page_num)
                total_answers = sum(len(q.get('answers', [])) for q in questions)
                print(f"   📈 Progress: {len(self.completed_pages)}/708 pages, "
                      f"{len(self.all_questions)} questions, "
                      f"{sum(len(q.get('answers', [])) for q in self.all_questions)} answers")
            else:
                self.failed_pages.add(page_num)
                print(f"   ❌ Page {page_num} failed")

    def load_progress(self) -> Dict[str, Any]:
        """Load previous scraping progress"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)
                    print(f"📂 Loaded progress: {len(progress.get('completed_pages', []))} pages completed, "
                          f"{len(progress.get('questions', []))} questions")
                    return progress
        except Exception as e:
            print(f"⚠️  Error loading progress: {e}")
        
        return {
            'completed_pages': [],
            'questions': [],
            'failed_pages': [],
            'stats': {}
        }

    def save_progress(self):
        """Save current progress with thread safety"""
        try:
            with self.lock:
                # Update statistics
                self.stats.update({
                    'last_update': datetime.now().isoformat(),
                    'total_questions': len(self.all_questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in self.all_questions),
                    'pages_completed': len(self.completed_pages),
                    'pages_failed': list(self.failed_pages)
                })
                
                # Calculate estimated completion
                if len(self.completed_pages) > 10:
                    elapsed = datetime.now() - datetime.fromisoformat(self.stats['start_time'])
                    pages_per_second = len(self.completed_pages) / elapsed.total_seconds()
                    remaining_pages = 708 - len(self.completed_pages)
                    eta_seconds = remaining_pages / pages_per_second if pages_per_second > 0 else 0
                    self.stats['estimated_completion'] = (datetime.now() + timedelta(seconds=eta_seconds)).isoformat()
                
                progress = {
                    'completed_pages': list(self.completed_pages),
                    'questions': self.all_questions,
                    'failed_pages': list(self.failed_pages),
                    'stats': self.stats,
                    'timestamp': datetime.now().isoformat()
                }
                
                # Save progress file
                with open(self.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(progress, f, indent=2, ensure_ascii=False)
                
                # Backup every 100 pages
                if len(self.completed_pages) % 100 == 0 and len(self.completed_pages) > 0:
                    backup_file = os.path.join(self.output_dir, f"parallel_backup_{len(self.completed_pages)}.json")
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(progress, f, indent=2, ensure_ascii=False)
                    print(f"   💾 Backup saved: {backup_file}")
                
        except Exception as e:
            print(f"⚠️  Error saving progress: {e}")

    def display_progress_stats(self):
        """Display current progress statistics"""
        with self.lock:
            if self.stats['start_time']:
                elapsed = datetime.now() - datetime.fromisoformat(self.stats['start_time'])
                completion_pct = (len(self.completed_pages) / 708) * 100
                
                print(f"\n📊 Parallel Progress Statistics:")
                print(f"   • Pages: {len(self.completed_pages)}/708 ({completion_pct:.1f}%)")
                print(f"   • Questions: {len(self.all_questions):,}")
                print(f"   • Answers: {sum(len(q.get('answers', [])) for q in self.all_questions):,}")
                print(f"   • Failed: {len(self.failed_pages)}")
                print(f"   • Elapsed: {elapsed}")
                print(f"   • Workers: {self.max_workers} concurrent")
                
                if len(self.completed_pages) > 0:
                    print(f"   • Avg time/page: {elapsed.total_seconds()/len(self.completed_pages):.1f}s")
                
                if self.stats.get('estimated_completion'):
                    eta = datetime.fromisoformat(self.stats['estimated_completion'])
                    print(f"   • ETA: {eta.strftime('%Y-%m-%d %H:%M:%S')}")
                print()

    async def scrape_all_pages_parallel(self, resume: bool = True) -> List[Dict[str, Any]]:
        """Main parallel scraping function"""
        
        print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PARALLEL PropertyGuru Scraper                            ║
║                                                                              ║
║  🚀 PARALLEL FEATURES:                                                       ║
║  ✅ {self.max_workers} concurrent pages processing simultaneously                      ║
║  ✅ 5-10x faster than sequential scraping                                   ║
║  ✅ Resume capability from last successful page                             ║
║  ✅ Thread-safe progress tracking                                           ║
║  ✅ Enhanced error handling per thread                                      ║
║  ✅ No testing limits - scrapes ALL questions per page                      ║
║                                                                              ║
║  Target: All 708 pages (~14,000+ questions with answers)                   ║
║  Estimated time: 2-4 hours (vs 12-24 hours sequential)                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)
        
        # Load previous progress if resuming
        if resume:
            progress = self.load_progress()
            self.completed_pages = set(progress.get('completed_pages', []))
            self.all_questions = progress.get('questions', [])
            self.failed_pages = set(progress.get('failed_pages', []))
            self.stats = progress.get('stats', self.stats)
        
        # Set start time if not resuming
        if not self.stats.get('start_time'):
            self.stats['start_time'] = datetime.now().isoformat()
        
        # Determine which pages to scrape
        all_pages = set(range(1, 709))  # Pages 1 to 708
        remaining_pages = all_pages - self.completed_pages - self.failed_pages
        
        print(f"🚀 Starting parallel scraping")
        print(f"📈 Already completed: {len(self.completed_pages)} pages")
        print(f"📈 Already have: {len(self.all_questions)} questions")
        print(f"🔄 Remaining: {len(remaining_pages)} pages")
        print(f"⚡ Concurrency: {self.max_workers} workers")
        print()
        
        if not remaining_pages:
            print("✅ All pages already completed!")
            return self.all_questions
        
        try:
            # Use ThreadPoolExecutor for parallel processing
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all remaining pages
                future_to_page = {
                    executor.submit(self.scrape_single_page_sync, page): page 
                    for page in remaining_pages
                }
                
                completed_count = 0
                
                # Process results as they complete
                for future in asyncio.as_completed([asyncio.wrap_future(f) for f in future_to_page]):
                    try:
                        page_num, questions = await future
                        self.process_page_result(page_num, questions)
                        
                        completed_count += 1
                        
                        # Save progress periodically
                        if completed_count % self.batch_size == 0:
                            self.save_progress()
                            self.display_progress_stats()
                        
                    except Exception as e:
                        print(f"💥 Error processing result: {e}")
                        continue
            
            # Final save
            self.save_progress()
            
        except KeyboardInterrupt:
            print("\n⏸️  Parallel scraping interrupted by user")
            self.save_progress()
            print("📁 Progress has been saved and can be resumed later")
            return self.all_questions
        
        # Save final comprehensive output
        await self.save_final_output()

        return self.all_questions

    async def save_final_output(self):
        """Save final comprehensive output"""
        try:
            with self.lock:
                end_time = datetime.now()
                start_time = datetime.fromisoformat(self.stats['start_time'])
                duration = end_time - start_time

                final_output = {
                    'metadata': {
                        'scraper_version': 'parallel_v1.0',
                        'source': 'PropertyGuru HDB Questions - All 708 Pages (Parallel)',
                        'scraping_start': self.stats['start_time'],
                        'scraping_end': end_time.isoformat(),
                        'duration': str(duration),
                        'duration_seconds': duration.total_seconds(),
                        'max_workers': self.max_workers,
                        'total_pages_attempted': 708,
                        'total_questions': len(self.all_questions),
                        'total_answers': sum(len(q.get('answers', [])) for q in self.all_questions),
                        'failed_pages_count': len(self.failed_pages),
                        'success_rate': ((708 - len(self.failed_pages)) / 708) * 100,
                        'avg_questions_per_page': len(self.all_questions) / 708 if len(self.all_questions) > 0 else 0,
                        'speedup_vs_sequential': f"{self.max_workers}x theoretical"
                    },
                    'failed_pages': list(self.failed_pages),
                    'questions': self.all_questions
                }

                # Save final comprehensive file
                final_file = os.path.join(self.output_dir, f"propertyguru_parallel_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
                with open(final_file, 'w', encoding='utf-8') as f:
                    json.dump(final_output, f, indent=2, ensure_ascii=False)

                print(f"\n🎉 PARALLEL SCRAPING COMPLETED!")
                print(f"📊 Final Statistics:")
                print(f"   • Total questions: {len(self.all_questions):,}")
                print(f"   • Total answers: {sum(len(q.get('answers', [])) for q in self.all_questions):,}")
                print(f"   • Duration: {duration}")
                print(f"   • Workers used: {self.max_workers}")
                print(f"   • Failed pages: {len(self.failed_pages)}")
                print(f"   • Success rate: {((708 - len(self.failed_pages)) / 708) * 100:.1f}%")
                print(f"   • Final output: {final_file}")

                if self.failed_pages:
                    print(f"   • Failed pages: {sorted(list(self.failed_pages))[:20]}{'...' if len(self.failed_pages) > 20 else ''}")

        except Exception as e:
            print(f"⚠️  Error saving final output: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get current scraping statistics"""
        try:
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)

                stats = progress.get('stats', {})
                questions = progress.get('questions', [])
                completed_pages = progress.get('completed_pages', [])

                return {
                    'completed_pages': len(completed_pages),
                    'total_questions': len(questions),
                    'total_answers': sum(len(q.get('answers', [])) for q in questions),
                    'failed_pages_count': len(progress.get('failed_pages', [])),
                    'completion_percentage': (len(completed_pages) / 708) * 100,
                    'last_update': stats.get('last_update', 'Unknown'),
                    'estimated_completion': stats.get('estimated_completion', 'Unknown'),
                    'max_workers': self.max_workers
                }
        except Exception as e:
            return {'error': f'Could not load stats: {e}'}

        return {'error': 'No progress data found'}

async def main():
    parser = argparse.ArgumentParser(description='Parallel PropertyGuru Scraper - All 708 Pages')
    parser.add_argument('--workers', type=int, default=5,
                       help='Number of concurrent workers (default: 5)')
    parser.add_argument('--no-resume', action='store_true',
                       help='Start fresh instead of resuming from previous progress')
    parser.add_argument('--stats', action='store_true',
                       help='Show current scraping statistics')

    args = parser.parse_args()

    scraper = ParallelPropertyGuruScraper(max_workers=args.workers)

    if args.stats:
        stats = scraper.get_stats()
        print("\n📊 PropertyGuru Parallel Scraping Statistics:")
        print("=" * 60)
        for key, value in stats.items():
            print(f"{key}: {value}")
        return

    # Main parallel scraping
    try:
        all_questions = await scraper.scrape_all_pages_parallel(resume=not args.no_resume)

        print(f"\n✅ Parallel scraping session completed!")
        print(f"📈 Total questions collected: {len(all_questions):,}")
        print(f"📈 Total answers collected: {sum(len(q.get('answers', [])) for q in all_questions):,}")

    except KeyboardInterrupt:
        print("\n⏸️  Parallel scraping interrupted by user")
        print("📁 Progress has been saved and can be resumed with:")
        print("   python parallel_propertyguru_scraper.py")

    except Exception as e:
        print(f"\n💥 Critical error: {e}")
        print("📁 Check progress files in data/processed/ to see what was scraped")

if __name__ == "__main__":
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PropertyGuru PARALLEL Scraper                            ║
║                                                                              ║
║  🎯 Target: All 708 pages (~14,000+ questions with answers)                ║
║  ⚡ Speed: 5-10x faster with parallel processing                            ║
║  ⏱️  Estimated time: 2-4 hours (vs 12-24 hours sequential)                  ║
║                                                                              ║
║  🚀 PARALLEL FEATURES:                                                       ║
║  ✅ Configurable concurrent workers (default: 5)                           ║
║  ✅ Thread-safe progress tracking and resume capability                     ║
║  ✅ Enhanced error handling per worker thread                               ║
║  ✅ Real-time monitoring with ETA calculations                              ║
║  ✅ No limits - scrapes ALL questions per page                              ║
║                                                                              ║
║  📋 USAGE:                                                                   ║
║  python parallel_propertyguru_scraper.py                                    ║
║  python parallel_propertyguru_scraper.py --workers 10                       ║
║  python parallel_propertyguru_scraper.py --no-resume                        ║
║  python parallel_propertyguru_scraper.py --stats                            ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
