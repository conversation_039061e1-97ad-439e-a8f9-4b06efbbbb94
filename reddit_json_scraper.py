#!/usr/bin/env python3
"""
Reddit JSON API Scraper - Uses Reddit's JSON API for better data extraction
"""

import urllib.request
import urllib.parse
import json
import os
import ssl
from datetime import datetime
import time

def create_ssl_context():
    """Create SSL context that handles certificate issues"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = ' '.join(text.split())
    
    # Remove common escape sequences
    replacements = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#x27;': "'",
        '&#39;': "'",
        '&nbsp;': ' '
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text.strip()

def scrape_reddit_json(url):
    """Scrape Reddit using JSON API"""
    try:
        # Convert URL to JSON API endpoint
        json_url = url.rstrip('/') + '.json'
        print(f"🔄 Scraping JSON: {json_url}")
        
        # Create SSL context
        ssl_context = create_ssl_context()
        
        # Create request with headers
        req = urllib.request.Request(
            json_url,
            headers={
                'User-Agent': 'Mozilla/5.0 (compatible; RedditScraper/1.0; Educational)',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9'
            }
        )
        
        # Make request
        with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
            data = json.loads(response.read().decode('utf-8'))
        
        print(f"   📄 JSON data loaded successfully")
        
        # Extract post data
        if not isinstance(data, list) or len(data) < 1:
            print("   ❌ Invalid JSON structure")
            return None
        
        post_data = data[0]['data']['children'][0]['data']
        
        # Extract basic info
        title = clean_text(post_data.get('title', ''))
        selftext = clean_text(post_data.get('selftext', ''))
        subreddit = post_data.get('subreddit', 'unknown')
        author = post_data.get('author', 'unknown')
        
        # Use selftext as question, fallback to title
        question = selftext if selftext else title
        
        print(f"   📝 Title: {title[:60]}...")
        print(f"   📄 Question: {question[:60]}...")
        
        # Extract comments
        comments = []
        if len(data) > 1 and 'data' in data[1]:
            comment_data = data[1]['data']['children']
            
            for comment_item in comment_data:
                if comment_item['kind'] == 't1':  # Comment type
                    comment = comment_item['data']
                    comment_body = clean_text(comment.get('body', ''))
                    comment_author = comment.get('author', 'unknown')
                    
                    if comment_body and len(comment_body) > 20 and comment_body != '[deleted]':
                        comments.append({
                            'text': comment_body,
                            'author': comment_author,
                            'date': datetime.now().isoformat(),
                            'score': comment.get('score', 0)
                        })
                        
                        if len(comments) >= 25:  # Limit to 25 comments
                            break
        
        result = {
            'url': url,
            'title': title,
            'question': question,
            'subreddit': subreddit,
            'author': author,
            'comments': comments,
            'scraped_at': datetime.now().isoformat()
        }
        
        print(f"✅ Success: {len(comments)} comments extracted")
        return result
        
    except Exception as e:
        print(f"💥 Error: {e}")
        return None

def convert_to_csv_format(reddit_data):
    """Convert to CSV format"""
    csv_rows = []
    
    for post in reddit_data:
        if not post:
            continue
        
        question = post['question']
        comments = post.get('comments', [])
        
        if not question:
            continue
        
        # Create CSV row
        row = {'question': question}
        
        # Add answers (unlimited)
        for i, comment in enumerate(comments, 1):
            row[f'answer{i}'] = comment['text']
        
        csv_rows.append(row)
        print(f"   📊 Question: '{question[:40]}...' with {len(comments)} answers")
    
    return csv_rows

def main():
    """Main function"""
    urls = [
        "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
        "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
        "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
        "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
        "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
        "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
        "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/"
    ]
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Reddit JSON API Scraper                                  ║
║                                                                              ║
║  🎯 Target: {len(urls)} Singapore housing/BTO Reddit posts                          ║
║  📊 Output: question, answer1, answer2, ... (unlimited)                     ║
║  🔧 Method: Reddit JSON API (.json endpoint)                                ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    all_data = []
    successful = 0
    
    for i, url in enumerate(urls, 1):
        print(f"\n🔄 Processing {i}/{len(urls)}")
        
        result = scrape_reddit_json(url)
        if result:
            all_data.append(result)
            successful += 1
        
        # Polite delay
        print(f"   ⏳ Waiting 2 seconds...")
        time.sleep(2)
    
    print(f"\n📊 Scraping completed: {successful}/{len(urls)} successful")
    
    if not all_data:
        print("❌ No data collected.")
        return None
    
    # Convert to CSV format
    print(f"\n🔄 Converting to CSV format...")
    csv_data = convert_to_csv_format(all_data)
    
    # Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_json_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_json_{timestamp}.csv"
    if csv_data:
        # Find all answer columns
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Headers
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            # Data
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    max_answers = max([len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data]) if csv_data else 0
    
    print(f"\n🎉 Reddit JSON scraping completed!")
    print(f"📊 Statistics:")
    print(f"   • Questions: {total_questions}")
    print(f"   • Answers: {total_answers}")
    print(f"   • Max answers per question: {max_answers}")
    print(f"   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}")
    
    print(f"\n📁 Files saved:")
    print(f"   • Raw data: {raw_file}")
    print(f"   • CSV format: {csv_file}")
    
    if total_questions > 0:
        print(f"\n🚀 Ready for ML validation!")
        print(f"   • PropertyGuru: 2,400 questions")
        print(f"   • Reddit: {total_questions} questions")
        print(f"   • Combined: {2400 + total_questions} questions for Singapore BERT vs Baseline BERT!")
        
        # Show sample questions
        print(f"\n📝 Sample Reddit Questions:")
        for i, row in enumerate(csv_data[:3], 1):
            question = row['question'][:80] + "..." if len(row['question']) > 80 else row['question']
            answer_count = len([k for k in row.keys() if k.startswith('answer')])
            print(f"   {i}. {question} ({answer_count} answers)")
    
    return csv_file

if __name__ == "__main__":
    main()
