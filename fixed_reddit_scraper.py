#!/usr/bin/env python3
"""
Fixed Reddit Scraper - Handles SSL issues and uses multiple approaches
"""

import urllib.request
import urllib.parse
import json
import os
import re
import ssl
from datetime import datetime
import time

def create_ssl_context():
    """Create SSL context that handles certificate issues"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def clean_text(text):
    """Clean extracted text"""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove common HTML entities
    replacements = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#x27;': "'",
        '&#39;': "'",
        '&nbsp;': ' ',
        '\\n': ' ',
        '\\t': ' ',
        '\\r': ' '
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    return text.strip()

def scrape_reddit_url(url):
    """Scrape Reddit URL with SSL fix"""
    try:
        print(f"🔄 Scraping: {url}")
        
        # Create SSL context that ignores certificate errors
        ssl_context = create_ssl_context()
        
        # Create request with headers
        req = urllib.request.Request(
            url,
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        )
        
        # Make request with SSL context
        with urllib.request.urlopen(req, timeout=30, context=ssl_context) as response:
            if response.info().get('Content-Encoding') == 'gzip':
                import gzip
                html = gzip.decompress(response.read()).decode('utf-8')
            else:
                html = response.read().decode('utf-8')
        
        print(f"   📄 Downloaded {len(html)} characters")
        
        # Extract title - multiple patterns
        title_patterns = [
            r'<title[^>]*>([^<]+)</title>',
            r'"title":"([^"]+)"',
            r'<h1[^>]*>([^<]+)</h1>',
            r'data-testid="post-content"[^>]*>.*?<h1[^>]*>([^<]+)</h1>'
        ]
        
        title = "Unknown Title"
        for pattern in title_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
            for match in matches:
                clean_title = clean_text(match)
                if clean_title and len(clean_title) > 5 and "reddit" not in clean_title.lower():
                    title = clean_title
                    break
            if title != "Unknown Title":
                break
        
        print(f"   📝 Title: {title[:50]}...")
        
        # Extract post content - multiple patterns
        post_patterns = [
            r'"selftext":"([^"]+)"',
            r'"text":"([^"]+)"',
            r'<div[^>]*data-click-id="text"[^>]*>([^<]+)</div>',
            r'<div[^>]*class="[^"]*usertext-body[^"]*"[^>]*>.*?<div[^>]*class="md"[^>]*>([^<]+)</div>',
            r'<p[^>]*>([^<]{20,})</p>'
        ]
        
        post_text = ""
        for pattern in post_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
            for match in matches:
                clean_post = clean_text(match)
                if clean_post and len(clean_post) > 15:
                    post_text = clean_post
                    break
            if post_text:
                break
        
        # If no post text, use title
        if not post_text or len(post_text) < 10:
            post_text = title
        
        print(f"   📄 Post: {post_text[:50]}...")
        
        # Extract comments - multiple approaches
        comments = []
        
        # Approach 1: JSON format comments
        json_patterns = [
            r'"body":"([^"]{30,})"',
            r'"text":"([^"]{30,})"',
            r'"content":"([^"]{30,})"'
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            for match in matches[:25]:  # Limit to 25 comments
                comment_text = clean_text(match)
                if comment_text and len(comment_text) > 30 and comment_text not in [c['text'] for c in comments]:
                    comments.append({
                        'text': comment_text,
                        'author': f'User_{len(comments)+1}',
                        'date': datetime.now().isoformat()
                    })
            if len(comments) >= 10:  # If we have enough comments, stop
                break
        
        # Approach 2: HTML format comments (if JSON didn't work well)
        if len(comments) < 5:
            html_patterns = [
                r'<div[^>]*class="[^"]*comment[^"]*"[^>]*>.*?<p[^>]*>([^<]{30,})</p>',
                r'<div[^>]*class="[^"]*usertext-body[^"]*"[^>]*>.*?<div[^>]*class="md"[^>]*>.*?<p[^>]*>([^<]{30,})</p>',
                r'<div[^>]*data-testid="comment"[^>]*>.*?<div[^>]*>([^<]{30,})</div>'
            ]
            
            for pattern in html_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE | re.DOTALL)
                for match in matches[:20]:
                    comment_text = clean_text(match)
                    if comment_text and len(comment_text) > 30 and comment_text not in [c['text'] for c in comments]:
                        comments.append({
                            'text': comment_text,
                            'author': f'User_{len(comments)+1}',
                            'date': datetime.now().isoformat()
                        })
                if len(comments) >= 10:
                    break
        
        # Extract subreddit from URL
        subreddit = "unknown"
        if "/r/" in url:
            subreddit = url.split("/r/")[1].split("/")[0]
        
        result = {
            'url': url,
            'title': title,
            'question': post_text,
            'subreddit': subreddit,
            'comments': comments,
            'scraped_at': datetime.now().isoformat(),
            'html_length': len(html)
        }
        
        print(f"✅ Success: {len(comments)} comments extracted")
        return result
        
    except Exception as e:
        print(f"💥 Error: {e}")
        return None

def convert_to_csv_format(reddit_data):
    """Convert to CSV format"""
    csv_rows = []
    
    for post in reddit_data:
        if not post or not post.get('comments'):
            continue
        
        question = post['question']
        comments = post['comments']
        
        if not question or not comments:
            continue
        
        # Create CSV row
        row = {'question': question}
        
        # Add answers (unlimited)
        for i, comment in enumerate(comments, 1):
            row[f'answer{i}'] = comment['text']
        
        csv_rows.append(row)
        print(f"   📊 Question with {len(comments)} answers")
    
    return csv_rows

def main():
    """Main function"""
    urls = [
        "https://www.reddit.com/r/singaporefi/comments/1aj7jdw/bto_advice/",
        "https://www.reddit.com/r/singaporefi/comments/1iwyrn8/advice_on_the_new_bto_rules_is_it_worth_it_vs/",
        "https://www.reddit.com/r/askSingapore/comments/1gg4lrl/whats_the_purpose_of_bto_when_it_is_always_over/",
        "https://www.reddit.com/r/singapore/comments/1ilx0uj/hdb_launches_5032_bto_flats_including_first/",
        "https://www.reddit.com/r/askSingapore/comments/1g3h2hr/parent_disagrees_with_my_bto_choice_opinions/",
        "https://www.reddit.com/r/askSingapore/comments/1ig2ihm/feeling_pressured_to_be_attached_because_of_bto/",
        "https://www.reddit.com/r/askSingapore/comments/1krqoqs/whats_your_bto_strategy_suggestions_ideas/"
    ]
    
    print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Fixed Reddit Scraper                                     ║
║                                                                              ║
║  🎯 Target: {len(urls)} Singapore housing/BTO Reddit posts                          ║
║  📊 Output: question, answer1, answer2, ... (unlimited)                     ║
║  🔧 Method: SSL-fixed urllib with multiple extraction patterns              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)
    
    all_data = []
    successful = 0
    
    for i, url in enumerate(urls, 1):
        print(f"\n🔄 Processing {i}/{len(urls)}")
        
        result = scrape_reddit_url(url)
        if result:
            all_data.append(result)
            if result.get('comments'):
                successful += 1
        
        # Polite delay
        print(f"   ⏳ Waiting 4 seconds...")
        time.sleep(4)
    
    print(f"\n📊 Scraping completed: {successful}/{len(urls)} with comments")
    
    if not all_data:
        print("❌ No data collected.")
        return None
    
    # Convert to CSV format
    print(f"\n🔄 Converting to CSV format...")
    csv_data = convert_to_csv_format(all_data)
    
    # Save results
    os.makedirs("data/processed", exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Save raw data
    raw_file = f"data/processed/reddit_fixed_{timestamp}.json"
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, indent=2, ensure_ascii=False)
    
    # Save CSV data
    csv_file = f"data/processed/reddit_fixed_{timestamp}.csv"
    if csv_data:
        # Find all answer columns
        all_columns = set()
        for row in csv_data:
            all_columns.update(row.keys())
        
        answer_columns = sorted([col for col in all_columns if col.startswith('answer')], 
                              key=lambda x: int(x.replace('answer', '')))
        columns = ['question'] + answer_columns
        
        with open(csv_file, 'w', encoding='utf-8') as f:
            # Headers
            f.write(','.join(f'"{col}"' for col in columns) + '\n')
            
            # Data
            for row in csv_data:
                csv_row = []
                for col in columns:
                    value = row.get(col, '').replace('"', '""')
                    csv_row.append(f'"{value}"')
                f.write(','.join(csv_row) + '\n')
    
    # Statistics
    total_questions = len(csv_data)
    total_answers = sum(len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data)
    max_answers = max([len([k for k in row.keys() if k.startswith('answer')]) for row in csv_data]) if csv_data else 0
    
    print(f"\n🎉 Reddit scraping completed!")
    print(f"📊 Statistics:")
    print(f"   • Questions: {total_questions}")
    print(f"   • Answers: {total_answers}")
    print(f"   • Max answers per question: {max_answers}")
    print(f"   • Avg answers per question: {total_answers/total_questions if total_questions > 0 else 0:.1f}")
    
    print(f"\n📁 Files saved:")
    print(f"   • Raw data: {raw_file}")
    print(f"   • CSV format: {csv_file}")
    
    if csv_data:
        print(f"\n🚀 Next: Combine with PropertyGuru data for ML validation!")
        print(f"   • PropertyGuru: 2,400 questions")
        print(f"   • Reddit: {total_questions} questions")
        print(f"   • Combined: {2400 + total_questions} questions for BERT comparison!")
    
    return csv_file

if __name__ == "__main__":
    main()
